import { google } from 'googleapis';
import { googleAuthService } from './google-auth';
import { storage } from '../storage';
import { type InsertReview } from '@shared/schema';

export class ReviewFetcherService {
  private lastSyncAttempt: Date | null = null;
  private syncInProgress: boolean = false;
  private quotaExceededUntil: Date | null = null;

  private isQuotaExceeded(): boolean {
    if (!this.quotaExceededUntil) return false;
    return new Date() < this.quotaExceededUntil;
  }

  private setQuotaExceeded(durationMinutes: number = 60): void {
    this.quotaExceededUntil = new Date(Date.now() + durationMinutes * 60 * 1000);
    console.log(`⚠️ API quota exceeded. Will retry after ${this.quotaExceededUntil.toLocaleTimeString()}`);
  }

  async fetchReviews(locationName: string, refreshToken: string): Promise<InsertReview[]> {
    try {
      // Check if quota is exceeded
      if (this.isQuotaExceeded()) {
        console.log('⚠️ Skipping review fetch due to quota limits');
        return [];
      }

      // Validate inputs
      if (!locationName || !refreshToken) {
        throw new Error('Location name and refresh token are required');
      }

      console.log('🔄 Starting review fetch for location:', locationName);

      // Set up authentication
      googleAuthService.setCredentials({ refresh_token: refreshToken });
      await googleAuthService.refreshAccessToken();

      // Fetch reviews using the Google Auth service
      const fetchedReviews = await googleAuthService.getReviews(locationName);
      const newReviews: InsertReview[] = [];
      const currentTime = new Date();

      console.log(`📥 Processing ${fetchedReviews.length} reviews from Google`);

      if (fetchedReviews.length === 0) {
        console.log('ℹ️ No reviews found from Google API');
        return [];
      }

      for (const review of fetchedReviews) {
        try {
          // Validate review data
          if (!review.name) {
            console.log('⚠️ Skipping review without Google ID');
            continue;
          }

          const reviewData: InsertReview = {
            googleId: review.name,
            rating: Math.max(1, Math.min(5, review.starRating || 1)), // Ensure rating is 1-5
            comment: review.comment || '',
            reviewerName: review.reviewer?.displayName || 'Anonymous',
            reviewDate: new Date(review.createTime || Date.now()).toISOString(),
            responseComment: review.reviewReply?.comment || null,
            responseDate: review.reviewReply ? new Date(review.reviewReply.updateTime || Date.now()).toISOString() : null,
            sentiment: this.determineSentiment(review.starRating || 1, review.comment || ''),
            fetchedAt: currentTime.toISOString()
          };

          // Check if review already exists
          const existing = await storage.getReviewByGoogleId(reviewData.googleId);
          if (!existing) {
            await storage.createReview(reviewData);
            newReviews.push(reviewData);
            console.log(`✅ New review added: ${reviewData.rating}⭐ by ${reviewData.reviewerName}`);
          } else {
            // Update existing review if response was added or modified
            let needsUpdate = false;
            const updates: Partial<InsertReview> = {};

            if (reviewData.responseComment && reviewData.responseComment !== existing.responseComment) {
              updates.responseComment = reviewData.responseComment;
              updates.responseDate = reviewData.responseDate;
              needsUpdate = true;
            }

            if (needsUpdate) {
              await storage.updateReview(existing.id, updates);
              console.log(`🔄 Updated review response for: ${reviewData.reviewerName}`);
            }
          }
        } catch (reviewError) {
          console.error('❌ Error processing individual review:', reviewError);
          // Continue processing other reviews
        }
      }

      console.log(`✅ Review fetch completed. ${newReviews.length} new reviews added`);
      return newReviews;
    } catch (error: any) {
      console.error('❌ Error fetching reviews:', error.message);

      // Handle quota exceeded
      if (error.message?.includes('Quota exceeded') ||
          error.message?.includes('quotaExceeded') ||
          error.status === 429) {
        this.setQuotaExceeded(60); // Set quota exceeded for 1 hour
        return []; // Return empty array instead of throwing
      }

      // Handle authentication errors
      if (error.message?.includes('invalid_grant') ||
          error.message?.includes('unauthorized')) {
        throw new Error('Authentication failed. Please reconnect your Google account.');
      }

      // Handle permission errors
      if (error.status === 403) {
        throw new Error('Insufficient permissions to access reviews. Please check your Google Business Profile permissions.');
      }

      throw new Error(`Failed to fetch reviews: ${error.message}`);
    }
  }

  private determineSentiment(rating: number, comment: string = ''): string {
    // Primary sentiment based on rating
    let sentimentFromRating: string;
    if (rating >= 4) sentimentFromRating = 'positive';
    else if (rating <= 2) sentimentFromRating = 'negative';
    else sentimentFromRating = 'neutral';

    // If no comment, use rating-based sentiment
    if (!comment.trim()) return sentimentFromRating;

    // Analyze comment text for more nuanced sentiment
    const text = comment.toLowerCase();
    const positiveWords = ['great', 'excellent', 'amazing', 'wonderful', 'fantastic', 'love', 'perfect', 'best', 'awesome', 'outstanding', 'superb', 'brilliant'];
    const negativeWords = ['terrible', 'awful', 'horrible', 'worst', 'hate', 'disgusting', 'disappointing', 'bad', 'poor', 'unacceptable', 'pathetic', 'useless'];

    const positiveCount = positiveWords.filter(word => text.includes(word)).length;
    const negativeCount = negativeWords.filter(word => text.includes(word)).length;

    // If comment sentiment strongly contradicts rating, use comment sentiment
    if (positiveCount > negativeCount + 1) return 'positive';
    if (negativeCount > positiveCount + 1) return 'negative';

    // Otherwise, use rating-based sentiment
    return sentimentFromRating;
  }

  async syncReviews(): Promise<{ success: boolean; newReviews: number; error?: string; quotaExceeded?: boolean }> {
    // Prevent concurrent sync operations
    if (this.syncInProgress) {
      console.log('⚠️ Sync already in progress, skipping...');
      return { success: false, newReviews: 0, error: 'Sync already in progress' };
    }

    // Check quota limits
    if (this.isQuotaExceeded()) {
      console.log('⚠️ Skipping sync due to quota limits');
      return { success: false, newReviews: 0, error: 'API quota exceeded', quotaExceeded: true };
    }

    this.syncInProgress = true;
    this.lastSyncAttempt = new Date();

    try {
      console.log('🔄 Starting review sync...');

      const businessInfo = await storage.getBusinessInfo();

      if (!businessInfo || !businessInfo.locationId || !businessInfo.refreshToken) {
        console.log('❌ Business not configured for sync');
        return { success: false, newReviews: 0, error: 'Business not configured' };
      }

      console.log(`📍 Syncing reviews for location: ${businessInfo.name}`);

      // Use locationId as locationName for the API call
      const newReviews = await this.fetchReviews(businessInfo.locationId, businessInfo.refreshToken);

      // Update last sync time only if successful
      await storage.createOrUpdateBusinessInfo({
        ...businessInfo,
        lastSync: new Date().toISOString()
      });

      console.log(`✅ Review sync completed successfully. ${newReviews.length} new reviews`);
      return { success: true, newReviews: newReviews.length };

    } catch (error: any) {
      console.error('❌ Review sync failed:', error.message);

      // Check if it's a quota error
      const isQuotaError = error.message?.includes('quota') ||
                          error.message?.includes('Quota') ||
                          error.status === 429;

      return {
        success: false,
        newReviews: 0,
        error: error.message,
        quotaExceeded: isQuotaError
      };
    } finally {
      this.syncInProgress = false;
    }
  }
}

export const reviewFetcherService = new ReviewFetcherService();
