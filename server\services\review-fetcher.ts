import { google } from 'googleapis';
import { googleAuthService } from './google-auth';
import { storage } from '../storage';
import { type InsertReview } from '@shared/schema';

export class ReviewFetcherService {
  async fetchReviews(locationName: string, refreshToken: string): Promise<InsertReview[]> {
    try {
      // Set up authentication
      googleAuthService.setCredentials({ refresh_token: refreshToken });
      await googleAuthService.refreshAccessToken();

      // Fetch reviews using the Google Auth service
      const fetchedReviews = await googleAuthService.getReviews(locationName);
      const newReviews: InsertReview[] = [];
      const currentTime = new Date();

      for (const review of fetchedReviews) {
        const reviewData: InsertReview = {
          googleId: review.name || '',
          rating: review.starRating || 0,
          comment: review.comment || '',
          reviewerName: review.reviewer?.displayName || 'Anonymous',
          reviewDate: new Date(review.createTime || Date.now()).toISOString(),
          responseComment: review.reviewReply?.comment || null,
          responseDate: review.reviewReply ? new Date(review.reviewReply.updateTime || Date.now()).toISOString() : null,
          sentiment: this.determineSentiment(review.starRating || 0),
          fetchedAt: currentTime.toISOString()
        };

        // Check if review already exists
        const existing = await storage.getReviewByGoogleId(reviewData.googleId);
        if (!existing) {
          const created = await storage.createReview(reviewData);
          newReviews.push(reviewData);
        } else {
          // Update existing review if response was added
          if (reviewData.responseComment && !existing.responseComment) {
            await storage.updateReview(existing.id, {
              responseComment: reviewData.responseComment,
              responseDate: reviewData.responseDate
            });
          }
        }
      }

      return newReviews;
    } catch (error) {
      console.error('Error fetching reviews:', error);
      throw error;
    }
  }

  private determineSentiment(rating: number): string {
    if (rating >= 4) return 'positive';
    if (rating <= 2) return 'negative';
    return 'neutral';
  }

  async syncReviews(): Promise<{ success: boolean; newReviews: number; error?: string }> {
    try {
      const businessInfo = await storage.getBusinessInfo();

      if (!businessInfo || !businessInfo.locationId || !businessInfo.refreshToken) {
        return { success: false, newReviews: 0, error: 'Business not configured' };
      }

      // Use locationId as locationName for the API call
      const newReviews = await this.fetchReviews(businessInfo.locationId, businessInfo.refreshToken);
      
      // Update last sync time
      await storage.createOrUpdateBusinessInfo({
        ...businessInfo,
        lastSync: new Date().toISOString()
      });

      return { success: true, newReviews: newReviews.length };
    } catch (error) {
      return { success: false, newReviews: 0, error: (error as Error).message };
    }
  }
}

export const reviewFetcherService = new ReviewFetcherService();
