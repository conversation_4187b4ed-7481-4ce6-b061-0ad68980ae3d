import { useEffect, useState } from "react";
import { useLocation } from "wouter";
import { useBusinessInfo } from "@/hooks/use-business-info";
import NavigationHeader from "@/components/navigation-header";
import StatisticsCards from "@/components/statistics-cards";
import RatingDistribution from "@/components/rating-distribution";
import ReviewFilters from "@/components/review-filters";
import ReviewList from "@/components/review-list";
import QuotaStatus from "@/components/quota-status";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Building2, Settings, RefreshCw } from "lucide-react";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";

export default function Dashboard() {
  const [, setLocation] = useLocation();
  const { data: businessData, isLoading } = useBusinessInfo();
  const [isRefreshing, setIsRefreshing] = useState(false);
  const { toast } = useToast();

  const handleRefreshBusiness = async () => {
    setIsRefreshing(true);
    try {
      const response = await apiRequest('POST', '/api/refresh-business');
      const result = await response.json();

      if (result.success) {
        toast({
          title: "Business data refreshed",
          description: "Your Google Business Profile data has been updated."
        });
        window.location.reload(); // Refresh to show new data
      } else {
        toast({
          title: "Refresh failed",
          description: result.error || "Unable to refresh business data. Google API quota may still be limited.",
          variant: "destructive"
        });
      }
    } catch (error) {
      toast({
        title: "Refresh failed",
        description: "Unable to refresh business data. Please try again later.",
        variant: "destructive"
      });
    } finally {
      setIsRefreshing(false);
    }
  };

  const handleTestQuota = async () => {
    try {
      const response = await apiRequest('GET', '/api/test-quota');
      const result = await response.json();

      if (result.quotaOk) {
        toast({
          title: "✅ Google API Ready",
          description: `Quota available! Found ${result.accountsFound} business accounts. ${result.canProceed ? 'You can now refresh your business data.' : 'Please check your Google Business Profile setup.'}`,
        });
      } else {
        toast({
          title: result.isQuotaError ? "⏳ Quota Exceeded" : "❌ API Error",
          description: result.message || result.error,
          variant: result.isQuotaError ? "default" : "destructive"
        });
      }
    } catch (error) {
      toast({
        title: "Test failed",
        description: "Unable to test Google API status.",
        variant: "destructive"
      });
    }
  };

  useEffect(() => {
    if (!isLoading && businessData && !businessData.isSetup) {
      setLocation("/setup");
    }
  }, [businessData, isLoading, setLocation]);

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading dashboard...</p>
        </div>
      </div>
    );
  }

  if (!businessData?.isSetup) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <Card className="w-full max-w-md mx-4">
          <CardContent className="pt-6 text-center">
            <Building2 className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h1 className="text-xl font-semibold mb-2">Setup Required</h1>
            <p className="text-muted-foreground mb-4">
              Please complete the setup to start managing your hotel reviews.
            </p>
            <Button onClick={() => setLocation("/setup")}>
              <Settings className="h-4 w-4 mr-2" />
              Start Setup
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-slate-50">
      <NavigationHeader />
      
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8 flex justify-between items-start">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Review Dashboard</h1>
            {businessData.businessInfo && (
              <>
                <p className="mt-2 text-gray-600">{businessData.businessInfo.name}</p>
                {businessData.businessInfo.address && (
                  <p className="text-sm text-gray-500">{businessData.businessInfo.address}</p>
                )}
              </>
            )}
          </div>
          
          {businessData?.businessInfo?.name === "Loading Business Information..." && (
            <div className="flex gap-2">
              <Button
                onClick={handleTestQuota}
                variant="outline"
                size="sm"
                className="flex items-center gap-2"
              >
                <Settings className="h-4 w-4" />
                Test Quota
              </Button>
              <Button
                onClick={handleRefreshBusiness}
                disabled={isRefreshing}
                variant="outline"
                className="flex items-center gap-2"
              >
                <RefreshCw className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
                {isRefreshing ? 'Fetching...' : 'Fetch Business Data'}
              </Button>
            </div>
          )}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6 mb-6">
          <div className="lg:col-span-3">
            <StatisticsCards />
          </div>
          <div className="lg:col-span-1">
            <QuotaStatus />
          </div>
        </div>

        <RatingDistribution />
        <ReviewFilters />
        <ReviewList />
      </main>
    </div>
  );
}
