import type { Express } from "express";
import { createServer, type Server } from "http";
import { storage } from "./storage";
import { googleAuthService } from "./services/google-auth";
import { reviewFetcherService } from "./services/review-fetcher";
import { updateReviewResponseSchema } from "@shared/schema";
import cron from 'node-cron';

// Background business data fetching
async function fetchBusinessDataInBackground(refreshToken: string): Promise<void> {
  let attempts = 0;
  const maxAttempts = 3;

  console.log('🔄 Starting background business data fetch...');
  
  while (attempts < maxAttempts) {
    try {
      console.log(`Attempting to fetch business data (attempt ${attempts + 1}/${maxAttempts})`);
      
      // Set up authentication
      googleAuthService.setCredentials({ refresh_token: refreshToken });
      await googleAuthService.refreshAccessToken();
      
      // Get business accounts
      const accounts = await googleAuthService.getBusinessAccounts();
      
      if (accounts.length > 0) {
        const account = accounts[0];
        console.log('Successfully fetched business account:', account.accountName);
        
        // Get business locations
        const locations = await googleAuthService.getBusinessLocations(account.name);
        
        if (locations.length > 0) {
          const location = locations[0];
          console.log('Successfully fetched business location:', location.title);
          
          // Update business info with real data
          await storage.createOrUpdateBusinessInfo({
            name: location.title || account.accountName || 'Your Business',
            address: location.storefrontAddress?.addressLines?.join(', ') || 'Address not available',
            accountId: account.name || '',
            locationId: location.name || '',
            refreshToken: refreshToken,
            lastSync: null
          });
          
          console.log('Business data updated successfully - triggering review sync');
          
          // Trigger review sync
          setTimeout(async () => {
            const syncResult = await reviewFetcherService.syncReviews();
            console.log('Review sync result:', syncResult);
          }, 1000);
          
          return;
        }
      }
      
      attempts++;
      if (attempts < maxAttempts) {
        const delay = 5000 * attempts;
        console.log(`Waiting ${delay/1000}s before retry...`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
      
    } catch (error: any) {
      attempts++;
      console.log(`Business data fetch attempt ${attempts} failed:`, error.message);
      
      if (attempts < maxAttempts) {
        const delay = 5000 * attempts;
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }

  console.log('❌ Business data fetch failed after all attempts');

  // Update business info to indicate failure
  try {
    const currentInfo = await storage.getBusinessInfo();
    if (currentInfo && currentInfo.locationId === 'pending-fetch') {
      await storage.createOrUpdateBusinessInfo({
        ...currentInfo,
        name: 'Business Setup Required',
        address: 'Failed to fetch business data from Google. Please try reconnecting your account.',
      });
      console.log('Updated business info to indicate setup failure');
    }
  } catch (updateError) {
    console.error('Failed to update business info after fetch failure:', updateError);
  }
}

export async function registerRoutes(app: Express): Promise<Server> {
  // Initialize database and run migrations if needed
  console.log('🔧 Initializing database...');

  // Only clear data in development mode if explicitly requested
  if (process.env.NODE_ENV === 'development' && process.env.CLEAR_DATA_ON_START === 'true') {
    console.log('⚠️ Development mode: Clearing all data as requested');
    await storage.clearAllData();
  } else {
    console.log('✅ Production mode: Preserving existing data');
  }

  // Setup automated review fetching every 30 minutes
  cron.schedule('*/30 * * * *', async () => {
    console.log('🔄 Running scheduled review sync...');
    try {
      const result = await reviewFetcherService.syncReviews();

      if (result.success) {
        console.log(`✅ Scheduled sync completed: ${result.newReviews} new reviews`);
      } else if (result.quotaExceeded) {
        console.log('⚠️ Scheduled sync skipped due to quota limits');
      } else {
        console.log(`❌ Scheduled sync failed: ${result.error}`);
      }
    } catch (error) {
      console.error('❌ Scheduled sync error:', error);
    }
  }, {
    scheduled: true,
    timezone: "UTC"
  });

  // Get business info and setup status
  app.get("/api/business", async (req, res) => {
    try {
      const businessInfo = await storage.getBusinessInfo();
      const isSetup = businessInfo && businessInfo.refreshToken;

      res.json({
        businessInfo,
        isSetup,
        setupUrl: isSetup ? null : googleAuthService.generateAuthUrl()
      });
    } catch (error) {
      console.log('No business info found (fresh start):', error);
      // Return default response for fresh setup
      res.json({
        businessInfo: null,
        isSetup: false,
        setupUrl: googleAuthService.generateAuthUrl()
      });
    }
  });

  // OAuth callback (both routes for compatibility)
  const handleOAuthCallback = async (req: any, res: any) => {
    try {
      const { code, error } = req.query;
      
      if (error) {
        console.error('OAuth error:', error);
        return res.redirect('/?setup=error&message=' + encodeURIComponent(error as string));
      }
      
      if (!code || typeof code !== 'string') {
        return res.redirect('/?setup=error&message=' + encodeURIComponent('Missing authorization code'));
      }

      console.log('Processing OAuth callback with code:', code.substring(0, 20) + '...');

      const tokens = await googleAuthService.getTokens(code);
      console.log('Successfully obtained tokens');
      
      try {
        // Get business accounts and locations
        const accounts = await googleAuthService.getBusinessAccounts();
        console.log('Found business accounts:', accounts.length);
        
        if (accounts.length === 0) {
          // Save tokens but mark as pending business data fetch
          await storage.createOrUpdateBusinessInfo({
            name: 'Loading Business Information...',
            address: 'Fetching business details from Google...',
            accountId: 'pending-fetch',
            locationId: 'pending-fetch',
            refreshToken: tokens.refresh_token || '',
            lastSync: null
          });
          
          console.log('✅ Authentication successful - will fetch business data in background');
          
          // Schedule immediate background fetch
          setTimeout(async () => {
            await fetchBusinessDataInBackground(tokens.refresh_token || '');
          }, 2000);
          
          return res.redirect('/dashboard');
        }

        const account = accounts[0];
        const locations = await googleAuthService.getBusinessLocations(account.name);
        console.log('Found business locations:', locations.length);
        
        if (locations.length === 0) {
          // Save account info even without locations
          await storage.createOrUpdateBusinessInfo({
            name: account.accountName || 'Your Business',
            address: 'Location details pending',
            accountId: account.name || '',
            locationId: 'no-location-found',
            refreshToken: tokens.refresh_token || '',
            lastSync: null
          });
          
          console.log('✅ Account connected - dashboard enabled');
          return res.redirect('/dashboard');
        }

        const location = locations[0];
        console.log('Using business location:', location.title);
        
        // Save complete business configuration
        await storage.createOrUpdateBusinessInfo({
          name: location.title || 'Unknown Business',
          address: location.storefrontAddress?.addressLines?.join(', ') || '',
          accountId: account.name || '',
          locationId: location.name || '',
          refreshToken: tokens.refresh_token || '',
          lastSync: null
        });

        console.log('✅ Complete business setup successful');

        // Attempt initial sync
        try {
          const syncResult = await reviewFetcherService.syncReviews();
          console.log('Initial sync result:', syncResult);
        } catch (syncError) {
          console.log('Initial sync skipped due to quota limits - will retry automatically');
        }

        res.redirect('/dashboard');
        
      } catch (businessError) {
        console.log('⚠️ Business API quota exceeded - proceeding with basic authentication');
        
        // Save minimal business info to enable dashboard access with valid tokens
        await storage.createOrUpdateBusinessInfo({
          name: 'Your Business Profile',
          address: 'Details will load when quota resets',
          accountId: 'quota-limited',
          locationId: 'quota-limited',
          refreshToken: tokens.refresh_token || '',
          lastSync: null
        });
        
        console.log('✅ Google authentication successful - dashboard ready');
        res.redirect('/dashboard');
      }
    } catch (error) {
      console.error('OAuth callback error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      res.redirect('/?setup=error&message=' + encodeURIComponent(errorMessage));
    }
  };

  // Register both callback routes for compatibility
  app.get("/api/auth/callback", handleOAuthCallback);
  app.get("/api/auth/google/callback", handleOAuthCallback);

  // Get reviews with filtering
  app.get("/api/reviews", async (req, res) => {
    try {
      const { rating, sentiment, limit = '20', offset = '0' } = req.query;
      
      const filters = {
        rating: rating ? parseInt(rating as string) : undefined,
        sentiment: sentiment as string,
        limit: parseInt(limit as string),
        offset: parseInt(offset as string)
      };

      const reviews = await storage.getReviews(filters);
      res.json(reviews);
    } catch (error) {
      res.status(500).json({ message: "Failed to get reviews" });
    }
  });

  // Get review statistics
  app.get("/api/reviews/stats", async (req, res) => {
    try {
      const stats = await storage.getReviewStats();
      res.json(stats);
    } catch (error) {
      res.status(500).json({ message: "Failed to get review stats" });
    }
  });

  // Add response to review
  app.post("/api/reviews/:id/response", async (req, res) => {
    try {
      const reviewId = parseInt(req.params.id);
      const body = updateReviewResponseSchema.parse(req.body);
      
      // Get the review to find its Google ID
      const review = await storage.getReviews();
      const targetReview = review.find(r => r.id === reviewId);
      
      if (!targetReview) {
        return res.status(404).json({ message: "Review not found" });
      }

      // Get business info for authentication
      const businessInfo = await storage.getBusinessInfo();
      
      if (!businessInfo || !businessInfo.refreshToken) {
        return res.status(400).json({ message: "Google Business account not configured" });
      }

      // Set up authentication for Google API
      googleAuthService.setCredentials({ refresh_token: businessInfo.refreshToken });
      await googleAuthService.refreshAccessToken();

      // Post response to Google My Business
      try {
        await googleAuthService.postReviewResponse(targetReview.googleId, body.responseComment);
        console.log(`Posted response to Google for review ${targetReview.googleId}`);
      } catch (googleError) {
        console.error('Failed to post to Google:', googleError);
        // Continue to update local storage even if Google API fails
      }
      
      // Update local storage
      const updatedReview = await storage.updateReview(reviewId, {
        responseComment: body.responseComment,
        responseDate: new Date().toISOString()
      });
      
      res.json(updatedReview);
    } catch (error) {
      console.error('Response error:', error);
      res.status(400).json({ message: "Failed to add response" });
    }
  });

  // Manual sync trigger with detailed quota information
  app.post("/api/sync", async (req, res) => {
    try {
      console.log('🔄 Manual sync requested');
      const result = await reviewFetcherService.syncReviews();

      if (result.success) {
        res.json({
          success: true,
          message: `Successfully synced ${result.newReviews} new reviews`,
          newReviews: result.newReviews
        });
      } else if (result.quotaExceeded) {
        res.json({
          success: false,
          message: 'Google API quota exceeded. The system will automatically retry when quota resets. This is normal behavior.',
          quotaExceeded: true,
          retryInfo: 'Quotas typically reset every minute for per-minute limits, and daily at midnight Pacific Time for daily limits.',
          explanation: 'Your Google Business Profile has 163 reviews, but API quotas prevent fetching them right now.'
        });
      } else {
        res.status(500).json({
          success: false,
          message: result.error || 'Sync failed'
        });
      }
    } catch (error: any) {
      console.error('❌ Manual sync error:', error);
      res.status(500).json({
        success: false,
        message: error.message || 'Internal server error'
      });
    }
  });

  // Quota status endpoint
  app.get('/api/quota-status', async (req, res) => {
    try {
      const businessInfo = await storage.getBusinessInfo();
      const quotaInfo = reviewFetcherService.getQuotaStatus();

      const isPendingFetch = businessInfo?.locationId === 'pending-fetch' || businessInfo?.accountId === 'pending-fetch';
      const isProperlyConfigured = !!(businessInfo?.locationId && businessInfo?.refreshToken && !isPendingFetch);

      const quotaStatus = {
        hasBusinessInfo: !!businessInfo,
        businessName: businessInfo?.name || null,
        lastSync: businessInfo?.lastSync || null,
        isConfigured: isProperlyConfigured,
        quota: quotaInfo,
        message: isPendingFetch
          ? 'Business data is being fetched from Google. Please wait...'
          : businessInfo && isProperlyConfigured
          ? `Business "${businessInfo.name}" is configured. ${quotaInfo.message}`
          : 'Business not configured. Please complete authentication first.',
        explanation: isPendingFetch
          ? 'Your Google Business Profile data is being retrieved. This may take a few moments.'
          : isProperlyConfigured
          ? 'Your 163 Google reviews will be fetched once API quota limits reset. This is normal behavior due to Google API restrictions.'
          : 'Complete the Google authentication to access your reviews.'
      };

      res.json(quotaStatus);
    } catch (error: any) {
      console.error('❌ Error checking quota status:', error);
      res.status(500).json({
        error: 'Failed to check quota status',
        message: error.message
      });
    }
  });

  // Force business data refresh
  app.post("/api/refresh-business", async (req, res) => {
    try {
      const businessInfo = await storage.getBusinessInfo();

      if (!businessInfo || !businessInfo.refreshToken) {
        return res.json({ success: false, error: 'No authentication found. Please reconnect your Google account.' });
      }

      console.log('🔄 Manual business data refresh requested...');
      await fetchBusinessDataInBackground(businessInfo.refreshToken);

      const updatedInfo = await storage.getBusinessInfo();
      res.json({ success: true, businessInfo: updatedInfo });
    } catch (error) {
      console.error('Business refresh error:', error);
      res.json({ success: false, error: (error as Error).message });
    }
  });

  // Logout endpoint - Clear authentication and business data
  app.post('/api/logout', async (_req, res) => {
    try {
      console.log('🚪 Logout requested - clearing authentication data');

      // Clear all business information and authentication data
      await storage.createOrUpdateBusinessInfo({
        name: 'Not Connected',
        address: 'Please connect your Google Business Profile',
        locationId: '',
        accountId: '',
        refreshToken: '',
        lastSync: null
      });

      // Optional: Clear all reviews (uncomment if you want to clear reviews on logout)
      // await storage.clearAllReviews();

      console.log('✅ Successfully logged out and cleared authentication data');

      res.json({
        success: true,
        message: 'Successfully logged out. All authentication data has been cleared.'
      });

    } catch (error) {
      console.error('❌ Error during logout:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to logout. Please try again.'
      });
    }
  });

  const httpServer = createServer(app);
  return httpServer;
}
