import { useQuery } from "@tanstack/react-query";
import type { Review } from "@shared/schema";

export function useReviews(filters: {
  rating?: number;
  sentiment?: string;
  search?: string;
  limit?: number;
  offset?: number;
} = {}) {
  const queryParams = new URLSearchParams();
  
  if (filters.rating) queryParams.set('rating', filters.rating.toString());
  if (filters.sentiment) queryParams.set('sentiment', filters.sentiment);
  if (filters.search) queryParams.set('search', filters.search);
  if (filters.limit) queryParams.set('limit', filters.limit.toString());
  if (filters.offset) queryParams.set('offset', filters.offset.toString());

  const queryString = queryParams.toString();
  const url = `/api/reviews${queryString ? `?${queryString}` : ''}`;

  return useQuery<Review[]>({
    queryKey: ['/api/reviews', filters],
  });
}
