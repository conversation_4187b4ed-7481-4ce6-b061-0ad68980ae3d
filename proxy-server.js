const express = require('express');
const { createProxyMiddleware } = require('http-proxy-middleware');

const app = express();

// Proxy OAuth callback to the main server
app.use('/api/auth/google/callback', createProxyMiddleware({
  target: 'http://localhost:8081',
  changeOrigin: true,
  logLevel: 'debug'
}));

// Proxy all other API requests to the main server
app.use('/api', createProxyMiddleware({
  target: 'http://localhost:8081',
  changeOrigin: true
}));

// Proxy frontend requests to the main server
app.use('/', createProxyMiddleware({
  target: 'http://localhost:8081',
  changeOrigin: true
}));

const PORT = 8080;
app.listen(PORT, () => {
  console.log(`🔄 Proxy server running on port ${PORT}`);
  console.log(`📡 Forwarding OAuth callbacks to main server on port 8081`);
});
