import { useState, useEffect } from "react";
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { apiRequest, queryClient } from "@/lib/queryClient";
import { Star } from "lucide-react";
import type { Review } from "@shared/schema";

interface ResponseModalProps {
  review: Review | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export default function ResponseModal({ review, open, onOpenChange }: ResponseModalProps) {
  const [response, setResponse] = useState("");
  const [submitting, setSubmitting] = useState(false);
  const { toast } = useToast();

  const handleSubmit = async () => {
    if (!review || !response.trim()) return;

    setSubmitting(true);
    try {
      await apiRequest('POST', `/api/reviews/${review.id}/response`, {
        responseComment: response.trim()
      });

      // Invalidate queries to refresh data
      queryClient.invalidateQueries({ queryKey: ['/api/reviews'] });
      queryClient.invalidateQueries({ queryKey: ['/api/reviews/stats'] });

      toast({
        title: "Response Posted",
        description: "Your response has been posted successfully.",
      });

      setResponse("");
      onOpenChange(false);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to post response. Please try again.",
        variant: "destructive",
      });
    } finally {
      setSubmitting(false);
    }
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`h-4 w-4 ${
          i < rating 
            ? 'text-hotel-yellow fill-current' 
            : 'text-gray-300'
        }`}
      />
    ));
  };

  const getSentimentColor = (sentiment: string) => {
    switch (sentiment) {
      case 'positive': return 'bg-green-100 text-green-800';
      case 'negative': return 'bg-red-100 text-red-800';
      default: return 'bg-yellow-100 text-yellow-800';
    }
  };

  // Reset response when modal closes or review changes
  useEffect(() => {
    if (!open) {
      setResponse(review?.responseComment || "");
    }
  }, [open, review]);

  if (!review) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>
            {review.responseComment ? 'Update Response' : 'Respond to Review'}
          </DialogTitle>
          <DialogDescription>
            Craft a thoughtful response to this customer review.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          <div className="p-4 bg-gray-50 rounded-lg">
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center gap-2">
                <span className="font-medium">{review.reviewerName}</span>
                <div className="flex">
                  {renderStars(review.rating)}
                </div>
                <Badge className={getSentimentColor(review.sentiment)}>
                  {review.sentiment}
                </Badge>
              </div>
            </div>
            <p className="text-gray-700 text-sm leading-relaxed">
              {review.comment}
            </p>
          </div>

          {review.responseComment && (
            <div className="p-4 bg-blue-50 border-l-4 border-hotel-blue rounded-lg">
              <p className="text-sm font-medium text-hotel-blue mb-2">
                Current Response:
              </p>
              <p className="text-sm text-gray-700">
                {review.responseComment}
              </p>
            </div>
          )}

          <div className="space-y-2">
            <Label htmlFor="response">
              {review.responseComment ? 'Updated Response' : 'Your Response'}
            </Label>
            <Textarea
              id="response"
              value={response}
              onChange={(e) => setResponse(e.target.value)}
              placeholder="Thank you for your feedback. We sincerely appreciate your review and would like to address your concerns..."
              rows={6}
              className="resize-none"
            />
            <p className="text-xs text-gray-500">
              {response.length}/1000 characters
            </p>
          </div>

          <div className="flex justify-end space-x-3">
            <Button
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={submitting}
            >
              Cancel
            </Button>
            <Button
              onClick={handleSubmit}
              disabled={submitting || !response.trim()}
              className="bg-hotel-blue hover:bg-hotel-blue/90"
            >
              {submitting ? 'Posting...' : review.responseComment ? 'Update Response' : 'Post Response'}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
