import { useState } from "react";
import { useReviews } from "@/hooks/use-reviews";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Star, Clock, Reply, Share, AlertTriangle } from "lucide-react";
import { format } from "date-fns";
import ReviewFilters from "./review-filters";
import ResponseModal from "./response-modal";
import type { Review } from "@shared/schema";

export default function ReviewList() {
  const [filters, setFilters] = useState<{
    rating?: number;
    sentiment?: string;
    search?: string;
  }>({});
  
  const [selectedReview, setSelectedReview] = useState<Review | null>(null);
  const [showResponseModal, setShowResponseModal] = useState(false);

  const { data: reviews, isLoading } = useReviews(filters);

  const getSentimentColor = (sentiment: string) => {
    switch (sentiment) {
      case 'positive': return 'bg-green-100 text-green-800';
      case 'negative': return 'bg-red-100 text-red-800';
      default: return 'bg-yellow-100 text-yellow-800';
    }
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`h-4 w-4 ${
          i < rating 
            ? 'text-hotel-yellow fill-current' 
            : 'text-gray-300'
        }`}
      />
    ));
  };

  const handleRespond = (review: Review) => {
    setSelectedReview(review);
    setShowResponseModal(true);
  };

  const handleShare = (review: Review) => {
    if (navigator.share) {
      navigator.share({
        title: `Review from ${review.reviewerName}`,
        text: review.comment,
        url: window.location.href,
      });
    } else {
      navigator.clipboard.writeText(
        `Review from ${review.reviewerName}: ${review.comment}`
      );
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <ReviewFilters onFiltersChange={setFilters} />
        {[...Array(3)].map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardContent className="p-6">
              <div className="h-32 bg-gray-200 rounded"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <ReviewFilters onFiltersChange={setFilters} />
      
      {reviews && reviews.length === 0 ? (
        <Card>
          <CardContent className="p-6 text-center">
            <p className="text-gray-500">No reviews found matching your criteria.</p>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-6">
          {reviews?.map((review) => (
            <Card key={review.id}>
              <CardContent className="p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center">
                    <div className="w-10 h-10 gradient-primary rounded-full flex items-center justify-center text-white font-semibold">
                      <span>{review.reviewerName.charAt(0).toUpperCase()}</span>
                    </div>
                    <div className="ml-3">
                      <h4 className="font-semibold text-gray-900">
                        {review.reviewerName}
                      </h4>
                      <p className="text-sm text-gray-500">
                        {format(new Date(review.reviewDate), 'MMMM d, yyyy')}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="flex items-center">
                      <div className="flex mr-2">
                        {renderStars(review.rating)}
                      </div>
                      <span className="text-sm font-medium text-gray-700">
                        {review.rating}.0
                      </span>
                    </div>
                    <Badge className={getSentimentColor(review.sentiment)}>
                      {review.sentiment.charAt(0).toUpperCase() + review.sentiment.slice(1)}
                    </Badge>
                  </div>
                </div>

                <div className="mb-4">
                  <p className="text-gray-700 leading-relaxed">
                    {review.comment}
                  </p>
                </div>

                {review.responseComment ? (
                  <div className="bg-blue-50 border-l-4 border-hotel-blue p-4 mb-4">
                    <div className="flex items-start">
                      <Reply className="h-4 w-4 text-hotel-blue mt-1 mr-2" />
                      <div>
                        <p className="text-sm font-medium text-hotel-blue">
                          Management Response
                        </p>
                        <p className="text-sm text-gray-700 mt-1">
                          {review.responseComment}
                        </p>
                        {review.responseDate && (
                          <p className="text-xs text-gray-500 mt-2">
                            Responded on {format(new Date(review.responseDate), 'MMMM d, yyyy')}
                          </p>
                        )}
                      </div>
                    </div>
                  </div>
                ) : review.sentiment === 'negative' && (
                  <Alert className="mb-4 border-red-200 bg-red-50">
                    <AlertTriangle className="h-4 w-4 text-red-500" />
                    <AlertDescription className="text-red-800">
                      This negative review requires management attention and response.
                    </AlertDescription>
                  </Alert>
                )}

                <div className="flex items-center justify-between pt-4 border-t border-gray-100">
                  <div className="flex items-center text-sm text-gray-500">
                    <Clock className="h-4 w-4 mr-1" />
                    <span>
                      Synced {format(new Date(review.fetchedAt), 'MMM d, h:mm a')}
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    {!review.responseComment ? (
                      <Button
                        onClick={() => handleRespond(review)}
                        className={
                          review.sentiment === 'negative'
                            ? 'bg-hotel-blue hover:bg-hotel-blue/90'
                            : ''
                        }
                        variant={review.sentiment === 'negative' ? 'default' : 'ghost'}
                      >
                        <Reply className="h-4 w-4 mr-1" />
                        {review.sentiment === 'negative' ? 'Respond Now' : 'Respond'}
                      </Button>
                    ) : (
                      <Button
                        onClick={() => handleRespond(review)}
                        variant="ghost"
                        size="sm"
                      >
                        <Reply className="h-4 w-4 mr-1" />
                        Update Response
                      </Button>
                    )}
                    <Button
                      onClick={() => handleShare(review)}
                      variant="ghost"
                      size="sm"
                    >
                      <Share className="h-4 w-4 mr-1" />
                      Share
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      <ResponseModal
        review={selectedReview}
        open={showResponseModal}
        onOpenChange={setShowResponseModal}
      />
    </div>
  );
}
