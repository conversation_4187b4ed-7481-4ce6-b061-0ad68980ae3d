import type {
  User,
  InsertUser,
  BusinessInfo,
  InsertBusinessInfo,
  Review,
  InsertReview
} from "../shared/schema.js";
import { drizzle } from "drizzle-orm/better-sqlite3";
import { users, businessInfo, reviews } from "../shared/schema.js";
import { eq, desc, and, count, avg, sql } from "drizzle-orm";
import Database from "better-sqlite3";

export interface IStorage {
  getUser(id: number): Promise<User | undefined>;
  getUserByUsername(username: string): Promise<User | undefined>;
  createUser(user: InsertUser): Promise<User>;
  
  getBusinessInfo(): Promise<BusinessInfo | undefined>;
  createOrUpdateBusinessInfo(info: InsertBusinessInfo): Promise<BusinessInfo>;
  
  getReviews(filters?: { rating?: number; sentiment?: string; limit?: number; offset?: number }): Promise<Review[]>;
  getReviewByGoogleId(googleId: string): Promise<Review | undefined>;
  createReview(review: InsertReview): Promise<Review>;
  updateReview(id: number, updates: Partial<Review>): Promise<Review>;
  getReviewStats(): Promise<{
    totalReviews: number;
    averageRating: number;
    ratingCounts: number[];
    sentimentCounts: { positive: number; neutral: number; negative: number };
    responseRate: number;
    thisMonth: number;
  }>;
  clearAllData(): Promise<void>;
}

export class SQLiteStorage implements IStorage {
  private db: ReturnType<typeof drizzle>;
  private sqlite: Database.Database;

  constructor(databasePath: string = "./database.sqlite") {
    try {
      this.sqlite = new Database(databasePath);

      // Enable WAL mode for better concurrent access
      this.sqlite.pragma('journal_mode = WAL');

      // Enable foreign key constraints
      this.sqlite.pragma('foreign_keys = ON');

      // Note: better-sqlite3 doesn't have a timeout method
      // Timeout is handled at the query level if needed

      this.db = drizzle(this.sqlite);
      console.log(`✅ SQLite database initialized at ${databasePath}`);

      // Test database connection
      this.testConnection();
    } catch (error) {
      console.error('❌ Failed to initialize SQLite database:', error);
      throw error;
    }
  }

  private testConnection(): void {
    try {
      this.sqlite.prepare('SELECT 1').get();
      console.log('✅ Database connection test successful');
    } catch (error) {
      console.error('❌ Database connection test failed:', error);
      throw error;
    }
  }

  async getUser(id: number): Promise<User | undefined> {
    try {
      const result = await this.db.select().from(users).where(eq(users.id, id)).limit(1);
      return result[0];
    } catch (error) {
      console.error('❌ Error getting user:', error);
      throw new Error('Failed to retrieve user');
    }
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    try {
      const result = await this.db.select().from(users).where(eq(users.username, username)).limit(1);
      return result[0];
    } catch (error) {
      console.error('❌ Error getting user by username:', error);
      throw new Error('Failed to retrieve user');
    }
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    try {
      const result = await this.db.insert(users).values(insertUser).returning();
      console.log('✅ User created successfully');
      return result[0];
    } catch (error: any) {
      console.error('❌ Error creating user:', error);
      if (error.message?.includes('UNIQUE constraint failed')) {
        throw new Error('Username already exists');
      }
      throw new Error('Failed to create user');
    }
  }

  async getBusinessInfo(): Promise<BusinessInfo | undefined> {
    try {
      const result = await this.db.select().from(businessInfo).limit(1);
      return result[0];
    } catch (error) {
      console.error('❌ Error getting business info:', error);
      throw new Error('Failed to retrieve business information');
    }
  }

  async createOrUpdateBusinessInfo(info: InsertBusinessInfo): Promise<BusinessInfo> {
    try {
      // Validate required fields (allow "disconnected" for logout)
      if (!info.name || (!info.accountId && info.accountId !== 'disconnected') || (!info.locationId && info.locationId !== 'disconnected')) {
        throw new Error('Missing required business information fields');
      }

      const existing = await this.getBusinessInfo();

      if (existing) {
        const result = await this.db
          .update(businessInfo)
          .set({
            name: info.name,
            address: info.address,
            accountId: info.accountId,
            locationId: info.locationId,
            refreshToken: info.refreshToken,
            lastSync: info.lastSync || null,
          })
          .where(eq(businessInfo.id, existing.id))
          .returning();
        console.log('✅ Business info updated successfully');
        return result[0];
      } else {
        const result = await this.db
          .insert(businessInfo)
          .values({
            ...info,
            lastSync: info.lastSync || null,
          })
          .returning();
        console.log('✅ Business info created successfully');
        return result[0];
      }
    } catch (error) {
      console.error('❌ Error creating/updating business info:', error);
      throw new Error('Failed to save business information');
    }
  }

  async getReviews(filters?: { rating?: number; sentiment?: string; limit?: number; offset?: number }): Promise<Review[]> {
    try {
      let query = this.db.select().from(reviews);

      const conditions = [];
      if (filters?.rating && filters.rating >= 1 && filters.rating <= 5) {
        conditions.push(eq(reviews.rating, filters.rating));
      }
      if (filters?.sentiment && ['positive', 'neutral', 'negative'].includes(filters.sentiment)) {
        conditions.push(eq(reviews.sentiment, filters.sentiment));
      }

      if (conditions.length > 0) {
        query = query.where(and(...conditions)) as any;
      }

      query = query.orderBy(desc(reviews.reviewDate)) as any;

      // Apply reasonable limits
      const limit = Math.min(filters?.limit || 20, 100);
      const offset = Math.max(filters?.offset || 0, 0);

      query = query.limit(limit) as any;
      if (offset > 0) {
        query = query.offset(offset) as any;
      }

      const result = await query;
      return result;
    } catch (error) {
      console.error('❌ Error getting reviews:', error);
      throw new Error('Failed to retrieve reviews');
    }
  }

  async getReviewByGoogleId(googleId: string): Promise<Review | undefined> {
    try {
      if (!googleId) {
        throw new Error('Google ID is required');
      }
      const result = await this.db.select().from(reviews).where(eq(reviews.googleId, googleId)).limit(1);
      return result[0];
    } catch (error) {
      console.error('❌ Error getting review by Google ID:', error);
      throw new Error('Failed to retrieve review');
    }
  }

  async createReview(insertReview: InsertReview): Promise<Review> {
    try {
      // Validate required fields
      if (!insertReview.googleId || !insertReview.reviewerName || !insertReview.reviewDate) {
        throw new Error('Missing required review fields');
      }

      // Validate rating
      if (insertReview.rating < 1 || insertReview.rating > 5) {
        throw new Error('Rating must be between 1 and 5');
      }

      // Validate sentiment
      if (!['positive', 'neutral', 'negative'].includes(insertReview.sentiment)) {
        throw new Error('Invalid sentiment value');
      }

      const result = await this.db
        .insert(reviews)
        .values(insertReview)
        .returning();

      console.log('✅ Review created successfully');
      return result[0];
    } catch (error: any) {
      console.error('❌ Error creating review:', error);
      if (error.message?.includes('UNIQUE constraint failed')) {
        throw new Error('Review already exists');
      }
      throw new Error('Failed to create review');
    }
  }

  async updateReview(id: number, updates: Partial<Review>): Promise<Review> {
    try {
      if (!id || id <= 0) {
        throw new Error('Valid review ID is required');
      }

      const result = await this.db
        .update(reviews)
        .set(updates)
        .where(eq(reviews.id, id))
        .returning();

      if (result.length === 0) {
        throw new Error('Review not found');
      }

      console.log('✅ Review updated successfully');
      return result[0];
    } catch (error) {
      console.error('❌ Error updating review:', error);
      throw new Error('Failed to update review');
    }
  }

  async getReviewStats(): Promise<{
    totalReviews: number;
    averageRating: number;
    ratingCounts: number[];
    sentimentCounts: { positive: number; neutral: number; negative: number };
    responseRate: number;
    thisMonth: number;
  }> {
    const allReviews = await this.db.select().from(reviews);

    if (allReviews.length === 0) {
      return {
        totalReviews: 0,
        averageRating: 0,
        ratingCounts: [0, 0, 0, 0, 0],
        sentimentCounts: { positive: 0, neutral: 0, negative: 0 },
        responseRate: 0,
        thisMonth: 0
      };
    }

    const totalReviews = allReviews.length;
    const averageRating = allReviews.reduce((sum, r) => sum + r.rating, 0) / totalReviews;

    const ratingCounts = [0, 0, 0, 0, 0];
    allReviews.forEach(r => {
      if (r.rating >= 1 && r.rating <= 5) {
        ratingCounts[r.rating - 1]++;
      }
    });

    const sentimentCounts = allReviews.reduce((acc, r) => {
      acc[r.sentiment as keyof typeof acc]++;
      return acc;
    }, { positive: 0, neutral: 0, negative: 0 });

    const reviewsWithResponses = allReviews.filter(r => r.responseComment).length;
    const responseRate = totalReviews > 0 ? (reviewsWithResponses / totalReviews) * 100 : 0;

    const thisMonth = allReviews.filter(r => {
      const reviewDate = new Date(r.reviewDate);
      const now = new Date();
      return reviewDate.getMonth() === now.getMonth() &&
             reviewDate.getFullYear() === now.getFullYear();
    }).length;

    return {
      totalReviews,
      averageRating: Math.round(averageRating * 10) / 10,
      ratingCounts,
      sentimentCounts,
      responseRate: Math.round(responseRate),
      thisMonth
    };
  }

  async clearAllData(): Promise<void> {
    await this.db.delete(reviews);
    await this.db.delete(businessInfo);
    await this.db.delete(users);
    console.log("🗑️ All data cleared from database");
  }
}

export class MemStorage implements IStorage {
  private users: Map<number, User>;
  private business: BusinessInfo | undefined;
  private reviewsList: Review[];
  private currentId: number;
  private currentReviewId: number;

  constructor() {
    this.users = new Map();
    this.business = undefined;
    this.reviewsList = [];
    this.currentId = 1;
    this.currentReviewId = 1;
    
    console.log('Storage initialized - ready for live Google Business data');
  }

  async getUser(id: number): Promise<User | undefined> {
    return this.users.get(id);
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    return Array.from(this.users.values()).find(
      (user) => user.username === username,
    );
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    const id = this.currentId++;
    const user: User = { ...insertUser, id };
    this.users.set(id, user);
    return user;
  }

  async getBusinessInfo(): Promise<BusinessInfo | undefined> {
    return this.business;
  }

  async createOrUpdateBusinessInfo(info: InsertBusinessInfo): Promise<BusinessInfo> {
    const businessInfo: BusinessInfo = {
      id: this.business?.id || 1,
      name: info.name,
      address: info.address ?? null,
      accountId: info.accountId,
      locationId: info.locationId,
      refreshToken: info.refreshToken ?? null,
      lastSync: info.lastSync ?? null,
    };
    this.business = businessInfo;
    return businessInfo;
  }

  async getReviews(filters?: { rating?: number; sentiment?: string; limit?: number; offset?: number }): Promise<Review[]> {
    let filtered = [...this.reviewsList];
    
    if (filters?.rating) {
      filtered = filtered.filter(r => r.rating === filters.rating);
    }
    
    if (filters?.sentiment) {
      filtered = filtered.filter(r => r.sentiment === filters.sentiment);
    }
    
    // Sort by review date descending
    filtered.sort((a, b) => new Date(b.reviewDate).getTime() - new Date(a.reviewDate).getTime());
    
    const offset = filters?.offset || 0;
    const limit = filters?.limit || 20;
    
    return filtered.slice(offset, offset + limit);
  }

  async getReviewByGoogleId(googleId: string): Promise<Review | undefined> {
    return this.reviewsList.find(r => r.googleId === googleId);
  }

  async createReview(insertReview: InsertReview): Promise<Review> {
    const id = this.currentReviewId++;
    const review: Review = { 
      id,
      googleId: insertReview.googleId,
      rating: insertReview.rating,
      comment: insertReview.comment ?? null,
      reviewerName: insertReview.reviewerName,
      reviewDate: insertReview.reviewDate,
      responseComment: insertReview.responseComment ?? null,
      responseDate: insertReview.responseDate ?? null,
      sentiment: insertReview.sentiment,
      fetchedAt: insertReview.fetchedAt
    };
    this.reviewsList.push(review);
    return review;
  }

  async updateReview(id: number, updates: Partial<Review>): Promise<Review> {
    const index = this.reviewsList.findIndex(r => r.id === id);
    if (index === -1) {
      throw new Error('Review not found');
    }
    
    this.reviewsList[index] = { ...this.reviewsList[index], ...updates };
    return this.reviewsList[index];
  }

  async getReviewStats(): Promise<{
    totalReviews: number;
    averageRating: number;
    ratingCounts: number[];
    sentimentCounts: { positive: number; neutral: number; negative: number };
    responseRate: number;
    thisMonth: number;
  }> {
    const reviews = this.reviewsList;
    const totalReviews = reviews.length;
    
    if (totalReviews === 0) {
      return {
        totalReviews: 0,
        averageRating: 0,
        ratingCounts: [0, 0, 0, 0, 0],
        sentimentCounts: { positive: 0, neutral: 0, negative: 0 },
        responseRate: 0,
        thisMonth: 0
      };
    }

    const averageRating = reviews.reduce((sum, r) => sum + r.rating, 0) / totalReviews;
    
    const ratingCounts = [0, 0, 0, 0, 0];
    reviews.forEach(r => {
      if (r.rating >= 1 && r.rating <= 5) {
        ratingCounts[r.rating - 1]++;
      }
    });

    const sentimentCounts = reviews.reduce((acc, r) => {
      acc[r.sentiment as keyof typeof acc]++;
      return acc;
    }, { positive: 0, neutral: 0, negative: 0 });

    const reviewsWithResponses = reviews.filter(r => r.responseComment).length;
    const responseRate = totalReviews > 0 ? (reviewsWithResponses / totalReviews) * 100 : 0;

    const thisMonth = reviews.filter(r => {
      const reviewDate = new Date(r.reviewDate);
      const now = new Date();
      return reviewDate.getMonth() === now.getMonth() && 
             reviewDate.getFullYear() === now.getFullYear();
    }).length;

    return {
      totalReviews,
      averageRating: Math.round(averageRating * 10) / 10,
      ratingCounts,
      sentimentCounts,
      responseRate: Math.round(responseRate),
      thisMonth
    };
  }

  async clearAllData(): Promise<void> {
    this.users.clear();
    this.business = undefined;
    this.reviewsList = [];
    this.currentId = 1;
    this.currentReviewId = 1;
    console.log("🗑️ All data cleared from memory storage");
  }
}

// Use SQLite storage by default, fallback to MemStorage if SQLite fails
let storage: IStorage;
try {
  storage = new SQLiteStorage();
  console.log('Using SQLite storage');
} catch (error) {
  console.warn('Failed to initialize SQLite storage, falling back to memory storage:', error);
  storage = new MemStorage();
}

export { storage };