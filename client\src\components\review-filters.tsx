import { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Search } from "lucide-react";

interface ReviewFiltersProps {
  onFiltersChange: (filters: {
    rating?: number;
    sentiment?: string;
    search?: string;
  }) => void;
}

export default function ReviewFilters({ onFiltersChange }: ReviewFiltersProps) {
  const [rating, setRating] = useState<string>("all");
  const [sentiment, setSentiment] = useState<string>("all");
  const [search, setSearch] = useState<string>("");

  const handleFiltersChange = () => {
    onFiltersChange({
      rating: rating !== "all" ? parseInt(rating) : undefined,
      sentiment: sentiment !== "all" ? sentiment : undefined,
      search: search || undefined,
    });
  };

  const clearFilters = () => {
    setRating("all");
    setSentiment("all");
    setSearch("");
    onFiltersChange({});
  };

  return (
    <Card className="mb-6">
      <CardContent className="p-6">
        <div className="flex flex-col sm:flex-row sm:items-end sm:justify-between gap-4">
          <div className="flex flex-wrap gap-4">
            <div className="min-w-0">
              <Label htmlFor="rating-filter" className="text-sm font-medium text-gray-700 mb-2 block">
                Filter by Rating
              </Label>
              <Select value={rating} onValueChange={setRating}>
                <SelectTrigger className="w-[140px]" id="rating-filter">
                  <SelectValue placeholder="All Ratings" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Ratings</SelectItem>
                  <SelectItem value="5">5 Stars</SelectItem>
                  <SelectItem value="4">4 Stars</SelectItem>
                  <SelectItem value="3">3 Stars</SelectItem>
                  <SelectItem value="2">2 Stars</SelectItem>
                  <SelectItem value="1">1 Star</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="min-w-0">
              <Label htmlFor="sentiment-filter" className="text-sm font-medium text-gray-700 mb-2 block">
                Sentiment
              </Label>
              <Select value={sentiment} onValueChange={setSentiment}>
                <SelectTrigger className="w-[140px]" id="sentiment-filter">
                  <SelectValue placeholder="All Sentiments" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Sentiments</SelectItem>
                  <SelectItem value="positive">Positive</SelectItem>
                  <SelectItem value="neutral">Neutral</SelectItem>
                  <SelectItem value="negative">Negative</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="flex gap-2">
              <Button
                onClick={handleFiltersChange}
                className="bg-hotel-blue hover:bg-hotel-blue/90"
              >
                Apply Filters
              </Button>
              <Button
                onClick={clearFilters}
                variant="outline"
              >
                Clear
              </Button>
            </div>
          </div>

          <div className="flex items-center gap-2">
            <div className="relative">
              <Input
                type="search"
                placeholder="Search reviews..."
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                onKeyDown={(e) => e.key === 'Enter' && handleFiltersChange()}
                className="pl-10 w-64"
              />
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
