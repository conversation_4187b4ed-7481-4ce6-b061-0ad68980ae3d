import { google } from 'googleapis';

interface RetryOptions {
  maxRetries: number;
  baseDelay: number;
  maxDelay: number;
}

export class GoogleAuthService {
  private oauth2Client: any;
  private readonly retryOptions: RetryOptions = {
    maxRetries: 3,
    baseDelay: 1000,
    maxDelay: 10000
  };

  constructor() {
    // Debug environment variables
    console.log('🔧 Initializing Google Auth Service...');
    console.log('GOOGLE_REDIRECT_URI:', process.env.GOOGLE_REDIRECT_URI);
    console.log('REPLIT_DOMAINS:', process.env.REPLIT_DOMAINS);

    // Use environment variable for redirect URI, with fallbacks
    const redirectUri = process.env.GOOGLE_REDIRECT_URI
      || (process.env.REPLIT_DOMAINS
          ? `https://${process.env.REPLIT_DOMAINS.split(',')[0]}/api/auth/callback`
          : 'http://localhost:8081/api/auth/google/callback');

    // Load Google OAuth credentials - fallback to direct values if env vars not available
    const clientId = process.env.GOOGLE_CLIENT_ID || '471900131350-7c4dg56v2oa9qm36ikndd358h26qv5di.apps.googleusercontent.com';
    const clientSecret = process.env.GOOGLE_CLIENT_SECRET || '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';

    console.log('✅ OAuth Redirect URI:', redirectUri);
    console.log('✅ Google Client ID loaded:', clientId.substring(0, 10) + '...');

    this.oauth2Client = new google.auth.OAuth2(
      clientId,
      clientSecret,
      redirectUri
    );
  }

  generateAuthUrl(): string {
    return this.oauth2Client.generateAuthUrl({
      access_type: 'offline',
      scope: [
        'https://www.googleapis.com/auth/business.manage'
      ],
      prompt: 'consent'
    });
  }

  private async delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  private async retryWithBackoff<T>(
    operation: () => Promise<T>,
    context: string
  ): Promise<T> {
    let lastError: Error;

    for (let attempt = 0; attempt <= this.retryOptions.maxRetries; attempt++) {
      try {
        if (attempt > 0) {
          const delay = Math.min(
            this.retryOptions.baseDelay * Math.pow(2, attempt - 1),
            this.retryOptions.maxDelay
          );
          console.log(`⏳ Retrying ${context} in ${delay}ms (attempt ${attempt + 1}/${this.retryOptions.maxRetries + 1})`);
          await this.delay(delay);
        }

        return await operation();
      } catch (error: any) {
        lastError = error;
        console.log(`❌ ${context} failed (attempt ${attempt + 1}):`, error.message);

        // Don't retry on certain errors
        if (error.message?.includes('invalid_grant') ||
            error.message?.includes('unauthorized') ||
            error.message?.includes('forbidden')) {
          throw error;
        }

        // If this is the last attempt, throw the error
        if (attempt === this.retryOptions.maxRetries) {
          throw error;
        }
      }
    }

    throw lastError!;
  }

  async getReviews(locationName: string) {
    return this.retryWithBackoff(async () => {
      google.options({ auth: this.oauth2Client });

      // Use the correct Google My Business Reviews API endpoint
      const response = await this.oauth2Client.request({
        url: `https://mybusinessreviews.googleapis.com/v1/${locationName}/reviews`,
        method: 'GET',
        params: {
          pageSize: 50,
          orderBy: 'createTime desc'
        }
      });

      const reviews = response.data.reviews || [];
      console.log(`✅ Successfully fetched ${reviews.length} reviews`);
      return reviews;
    }, 'fetch reviews').catch((error: any) => {
      console.error('❌ Error getting reviews:', error.message);

      // Handle quota exceeded specifically
      if (error.message?.includes('Quota exceeded') ||
          error.message?.includes('quotaExceeded') ||
          error.status === 429) {
        console.log('⚠️ Google API quota exceeded for reviews. Returning empty array...');
        return [];
      }

      // Handle permission errors
      if (error.status === 403 || error.message?.includes('permission')) {
        throw new Error('Insufficient permissions to access reviews. Please ensure your Google Business Profile has review access enabled.');
      }

      // Handle not found errors
      if (error.status === 404) {
        throw new Error('Business location not found. Please verify your Google Business Profile setup.');
      }

      throw new Error(`Unable to fetch reviews: ${error.message}`);
    });
  }

  async getTokens(code: string) {
    return this.retryWithBackoff(async () => {
      const { tokens } = await this.oauth2Client.getToken(code);
      this.oauth2Client.setCredentials(tokens);
      console.log('✅ Successfully obtained OAuth tokens');
      return tokens;
    }, 'get OAuth tokens').catch((error: any) => {
      console.error('❌ Error getting tokens:', error.message);

      if (error.message?.includes('invalid_grant')) {
        throw new Error('Authorization code expired or already used. Please try authorizing again.');
      }

      if (error.message?.includes('invalid_client')) {
        throw new Error('Invalid OAuth client configuration. Please check your Google API credentials.');
      }

      throw new Error(`OAuth token exchange failed: ${error.message}`);
    });
  }

  setCredentials(tokens: any) {
    this.oauth2Client.setCredentials(tokens);
    console.log('✅ OAuth credentials set');
  }

  async refreshAccessToken() {
    return this.retryWithBackoff(async () => {
      const { token } = await this.oauth2Client.getAccessToken();
      console.log('✅ Access token refreshed successfully');
      return token;
    }, 'refresh access token').catch((error: any) => {
      console.error('❌ Error refreshing access token:', error.message);

      if (error.message?.includes('invalid_grant')) {
        throw new Error('Refresh token expired. Please re-authorize the application.');
      }

      throw new Error(`Token refresh failed: ${error.message}`);
    });
  }

  getAuthClient() {
    return this.oauth2Client;
  }

  async getBusinessAccounts() {
    return this.retryWithBackoff(async () => {
      google.options({ auth: this.oauth2Client });

      // Use the correct Google My Business Account Management API
      const response = await this.oauth2Client.request({
        url: 'https://mybusinessaccountmanagement.googleapis.com/v1/accounts',
        method: 'GET'
      });

      const accounts = response.data.accounts || [];
      console.log(`✅ Successfully fetched ${accounts.length} business accounts`);
      return accounts;
    }, 'fetch business accounts').catch((error: any) => {
      console.error('❌ Error getting business accounts:', error.message);

      // Handle quota exceeded specifically
      if (error.message?.includes('Quota exceeded') ||
          error.message?.includes('quotaExceeded') ||
          error.status === 429) {
        console.log('⚠️ Google API quota exceeded for accounts. Returning empty array...');
        return [];
      }

      // Handle permission errors
      if (error.status === 403) {
        throw new Error('Insufficient permissions to access business accounts. Please ensure you have a Google Business Profile and the necessary permissions.');
      }

      throw new Error(`Unable to access business accounts: ${error.message}`);
    });
  }

  async getBusinessLocations(accountName: string) {
    return this.retryWithBackoff(async () => {
      google.options({ auth: this.oauth2Client });

      // Use the correct Google My Business Business Information API
      const response = await this.oauth2Client.request({
        url: `https://mybusinessbusinessinformation.googleapis.com/v1/${accountName}/locations`,
        method: 'GET',
        params: {
          pageSize: 100,
          readMask: 'name,title,storefrontAddress,websiteUri,phoneNumbers'
        }
      });

      const locations = response.data.locations || [];
      console.log(`✅ Successfully fetched ${locations.length} business locations`);
      return locations;
    }, 'fetch business locations').catch((error: any) => {
      console.error('❌ Error getting business locations:', error.message);

      // Handle quota exceeded
      if (error.message?.includes('Quota exceeded') ||
          error.message?.includes('quotaExceeded') ||
          error.status === 429) {
        console.log('⚠️ Google API quota exceeded for locations. Returning empty array...');
        return [];
      }

      // Handle permission errors
      if (error.status === 403) {
        throw new Error('Insufficient permissions to access business locations. Please ensure your Google Business Profile is verified.');
      }

      // Handle not found
      if (error.status === 404) {
        throw new Error('Business account not found. Please verify your Google Business Profile setup.');
      }

      throw new Error(`Unable to find business locations: ${error.message}`);
    });
  }

  async postReviewResponse(reviewName: string, responseComment: string) {
    return this.retryWithBackoff(async () => {
      google.options({ auth: this.oauth2Client });

      const response = await this.oauth2Client.request({
        url: `https://mybusinessreviews.googleapis.com/v1/${reviewName}/reply`,
        method: 'PUT',
        data: {
          comment: responseComment
        }
      });

      console.log('✅ Successfully posted review response');
      return response.data;
    }, 'post review response').catch((error: any) => {
      console.error('❌ Error posting review response:', error.message);

      // Handle quota exceeded
      if (error.message?.includes('Quota exceeded') ||
          error.message?.includes('quotaExceeded') ||
          error.status === 429) {
        throw new Error('Google API quota exceeded. Please try again later.');
      }

      // Handle permission errors
      if (error.status === 403) {
        throw new Error('Insufficient permissions to respond to reviews. Please ensure your Google Business Profile has response permissions enabled.');
      }

      // Handle not found
      if (error.status === 404) {
        throw new Error('Review not found or no longer available for response.');
      }

      throw new Error(`Unable to post response: ${error.message}`);
    });
  }
}

let _googleAuthService: GoogleAuthService | null = null;

export const googleAuthService = {
  get instance(): GoogleAuthService {
    if (!_googleAuthService) {
      _googleAuthService = new GoogleAuthService();
    }
    return _googleAuthService;
  },

  // Proxy methods to the instance
  generateAuthUrl(): string {
    return this.instance.generateAuthUrl();
  },

  async getTokens(code: string) {
    return this.instance.getTokens(code);
  },

  setCredentials(tokens: any) {
    return this.instance.setCredentials(tokens);
  },

  async refreshAccessToken() {
    return this.instance.refreshAccessToken();
  },

  getAuthClient() {
    return this.instance.getAuthClient();
  },

  async getBusinessAccounts() {
    return this.instance.getBusinessAccounts();
  },

  async getBusinessLocations(accountName: string) {
    return this.instance.getBusinessLocations(accountName);
  },

  async getReviews(locationName: string) {
    return this.instance.getReviews(locationName);
  },

  async postReviewResponse(reviewName: string, responseComment: string) {
    return this.instance.postReviewResponse(reviewName, responseComment);
  }
};
