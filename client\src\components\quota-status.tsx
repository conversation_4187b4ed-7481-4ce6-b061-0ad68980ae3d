import { useQuery } from "@tanstack/react-query";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { <PERSON>, AlertTriangle, CheckCircle, Info } from "lucide-react";

interface QuotaStatusData {
  hasBusinessInfo: boolean;
  businessName: string | null;
  lastSync: string | null;
  isConfigured: boolean;
  quota: {
    exceeded: boolean;
    retryAfter: string | null;
    message: string;
  };
  message: string;
  explanation: string;
}

export default function QuotaStatus() {
  const { data: quotaStatus, isLoading } = useQuery<QuotaStatusData>({
    queryKey: ['/api/quota-status'],
    refetchInterval: 30000, // Check every 30 seconds
  });

  if (isLoading) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center space-x-2">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
            <span className="text-sm text-muted-foreground">Checking API status...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!quotaStatus) return null;

  const getStatusIcon = () => {
    if (!quotaStatus.isConfigured) return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
    if (quotaStatus.quota.exceeded) return <Clock className="h-4 w-4 text-orange-500" />;
    return <CheckCircle className="h-4 w-4 text-green-500" />;
  };

  const getStatusBadge = () => {
    if (!quotaStatus.isConfigured) return <Badge variant="secondary">Not Configured</Badge>;
    if (quotaStatus.quota.exceeded) return <Badge variant="outline" className="text-orange-600 border-orange-200">Quota Exceeded</Badge>;
    return <Badge variant="outline" className="text-green-600 border-green-200">Ready</Badge>;
  };

  return (
    <Card>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center justify-between text-base">
          <div className="flex items-center space-x-2">
            {getStatusIcon()}
            <span>Google API Status</span>
          </div>
          {getStatusBadge()}
        </CardTitle>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="space-y-3">
          <div className="text-sm text-muted-foreground">
            {quotaStatus.message}
          </div>
          
          {quotaStatus.quota.exceeded && quotaStatus.quota.retryAfter && (
            <Alert>
              <Clock className="h-4 w-4" />
              <AlertDescription>
                <strong>Quota Reset:</strong> {new Date(quotaStatus.quota.retryAfter).toLocaleTimeString()}
                <br />
                <span className="text-xs text-muted-foreground mt-1 block">
                  Google Business Profile API has strict limits. This is normal behavior.
                </span>
              </AlertDescription>
            </Alert>
          )}

          {quotaStatus.isConfigured && (
            <Alert>
              <Info className="h-4 w-4" />
              <AlertDescription>
                <strong>Your Reviews:</strong> {quotaStatus.explanation}
                <br />
                <span className="text-xs text-muted-foreground mt-1 block">
                  The system automatically retries every 30 minutes when quota allows.
                </span>
              </AlertDescription>
            </Alert>
          )}

          {quotaStatus.lastSync && (
            <div className="text-xs text-muted-foreground">
              Last successful sync: {new Date(quotaStatus.lastSync).toLocaleString()}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
