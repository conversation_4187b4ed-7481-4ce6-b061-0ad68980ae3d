@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: hsl(0, 0%, 100%);
  --foreground: hsl(240, 10%, 9%);
  --muted: hsl(210, 40%, 96%);
  --muted-foreground: hsl(215, 16%, 47%);
  --popover: hsl(0, 0%, 100%);
  --popover-foreground: hsl(240, 10%, 9%);
  --card: hsl(0, 0%, 100%);
  --card-foreground: hsl(240, 10%, 9%);
  --border: hsl(214, 32%, 91%);
  --input: hsl(214, 32%, 91%);
  --primary: hsl(231, 58%, 58%);
  --primary-foreground: hsl(0, 0%, 100%);
  --secondary: hsl(210, 40%, 96%);
  --secondary-foreground: hsl(222, 84%, 5%);
  --accent: hsl(210, 40%, 96%);
  --accent-foreground: hsl(222, 84%, 5%);
  --destructive: hsl(0, 84%, 60%);
  --destructive-foreground: hsl(210, 40%, 98%);
  --ring: hsl(231, 58%, 58%);
  --radius: 0.5rem;
  
  /* Custom hotel theme colors */
  --hotel-blue: hsl(220, 84%, 56%);
  --hotel-green: hsl(160, 84%, 39%);
  --hotel-red: hsl(0, 84%, 49%);
  --hotel-yellow: hsl(43, 96%, 56%);
  --hotel-purple: hsl(271, 76%, 53%);
  --gradient-primary: linear-gradient(135deg, hsl(231, 58%, 58%) 0%, hsl(271, 76%, 53%) 100%);
}

.dark {
  --background: hsl(240, 10%, 4%);
  --foreground: hsl(0, 0%, 98%);
  --muted: hsl(240, 4%, 16%);
  --muted-foreground: hsl(240, 5%, 65%);
  --popover: hsl(240, 10%, 4%);
  --popover-foreground: hsl(0, 0%, 98%);
  --card: hsl(240, 10%, 4%);
  --card-foreground: hsl(0, 0%, 98%);
  --border: hsl(240, 4%, 16%);
  --input: hsl(240, 4%, 16%);
  --primary: hsl(231, 58%, 58%);
  --primary-foreground: hsl(0, 0%, 100%);
  --secondary: hsl(240, 4%, 16%);
  --secondary-foreground: hsl(0, 0%, 98%);
  --accent: hsl(240, 4%, 16%);
  --accent-foreground: hsl(0, 0%, 98%);
  --destructive: hsl(0, 63%, 31%);
  --destructive-foreground: hsl(0, 0%, 98%);
  --ring: hsl(231, 58%, 58%);
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply font-sans antialiased bg-background text-foreground;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, system-ui, sans-serif;
  }
}

@layer utilities {
  .gradient-primary {
    background: var(--gradient-primary);
  }
  
  .text-hotel-blue {
    color: var(--hotel-blue);
  }
  
  .bg-hotel-blue {
    background-color: var(--hotel-blue);
  }
  
  .text-hotel-green {
    color: var(--hotel-green);
  }
  
  .bg-hotel-green {
    background-color: var(--hotel-green);
  }
  
  .text-hotel-red {
    color: var(--hotel-red);
  }
  
  .bg-hotel-red {
    background-color: var(--hotel-red);
  }
  
  .text-hotel-yellow {
    color: var(--hotel-yellow);
  }
  
  .bg-hotel-yellow {
    background-color: var(--hotel-yellow);
  }
}
