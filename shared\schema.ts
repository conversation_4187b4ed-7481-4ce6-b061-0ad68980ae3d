import { sqliteTable, text, integer, blob } from "drizzle-orm/sqlite-core";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";

export const users = sqliteTable("users", {
  id: integer("id").primaryKey({ autoIncrement: true }),
  username: text("username").notNull().unique(),
  password: text("password").notNull(),
});

export const businessInfo = sqliteTable("business_info", {
  id: integer("id").primary<PERSON>ey({ autoIncrement: true }),
  name: text("name").notNull(),
  address: text("address"),
  accountId: text("account_id").notNull(),
  locationId: text("location_id").notNull(),
  refreshToken: text("refresh_token"),
  lastSync: text("last_sync"), // SQLite doesn't have native timestamp, using text
});

export const reviews = sqliteTable("reviews", {
  id: integer("id").primary<PERSON>ey({ autoIncrement: true }),
  googleId: text("google_id").notNull().unique(),
  rating: integer("rating").notNull(),
  comment: text("comment").default(""),
  reviewerName: text("reviewer_name").notNull(),
  reviewDate: text("review_date").notNull(), // SQLite doesn't have native timestamp, using text
  responseComment: text("response_comment"),
  responseDate: text("response_date"), // SQLite doesn't have native timestamp, using text
  sentiment: text("sentiment").notNull(), // positive, negative, neutral
  fetchedAt: text("fetched_at").notNull(), // SQLite doesn't have native timestamp, using text
});

export const insertUserSchema = createInsertSchema(users).pick({
  username: true,
  password: true,
});

export const insertBusinessInfoSchema = createInsertSchema(businessInfo).omit({
  id: true,
});

export const insertReviewSchema = createInsertSchema(reviews).omit({
  id: true,
});

export const updateReviewResponseSchema = z.object({
  responseComment: z.string().min(1),
});

export type InsertUser = z.infer<typeof insertUserSchema>;
export type User = typeof users.$inferSelect;
export type BusinessInfo = typeof businessInfo.$inferSelect;
export type InsertBusinessInfo = z.infer<typeof insertBusinessInfoSchema>;
export type Review = typeof reviews.$inferSelect;
export type InsertReview = z.infer<typeof insertReviewSchema>;
