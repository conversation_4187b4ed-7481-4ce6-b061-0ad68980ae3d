# HotelReviewHub - Production Deployment Guide

## 🚀 Quick Deployment

### Prerequisites
- Node.js 20+
- Google Cloud Console project with Business Profile API enabled
- Google Business Profile with management permissions

### Environment Setup
```bash
# Required Environment Variables
NODE_ENV=production
PORT=8081
GOOGLE_CLIENT_ID=************-7c4dg56v2oa9qm36ikndd358h26qv5di.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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
GOOGLE_REDIRECT_URI=http://localhost:8081/api/auth/google/callback
DATABASE_URL=./database.sqlite
```

### Production Build & Start
```bash
# 1. Install dependencies
npm install

# 2. Build for production
npm run build

# 3. Start production server
npm start
```

## 🔧 Google Cloud Console Configuration

### Required APIs
Enable these APIs in your Google Cloud Console:
- Google Business Profile API (Account Management v1)
- Google Business Profile API (Business Information v1)
- Google Business Profile API (Reviews v1)

### OAuth Configuration
1. Go to Google Cloud Console → APIs & Services → Credentials
2. Find OAuth Client ID: `************-7c4dg56v2oa9qm36ikndd358h26qv5di.apps.googleusercontent.com`
3. Configure:
   - **Authorized JavaScript origins**: `http://localhost:8081`
   - **Authorized redirect URIs**: `http://localhost:8081/api/auth/google/callback`

## 📊 Production Features

### ✅ What's Included
- **Automated Review Sync**: Every 30 minutes with error recovery
- **Intelligent Quota Management**: Handles Google API limits gracefully
- **Professional Dashboard**: Real-time analytics and review management
- **Direct Response System**: Reply to Google reviews directly
- **Advanced Sentiment Analysis**: AI-powered review categorization
- **Production Database**: SQLite with WAL mode and optimizations
- **Comprehensive Logging**: Detailed monitoring and troubleshooting

### 🛡️ Security Features
- Secure OAuth token management with automatic refresh
- Environment variable protection
- Input validation and sanitization
- Error boundary protection

## 🔍 Monitoring & Troubleshooting

### Health Checks
- Application: `http://localhost:8081/api/business`
- Database: SQLite file at `./database.sqlite`
- Logs: Check console output for detailed operation logs

### Common Issues
- **Quota Exceeded**: Normal behavior, system handles automatically
- **Authentication Issues**: Verify Google Cloud Console configuration
- **Database Issues**: Check SQLite file permissions and disk space

## 📈 Performance Metrics

### Expected Performance
- **Page Load**: <2 seconds
- **API Response**: <500ms average
- **Review Sync**: 30-minute intervals
- **API Usage**: ~50-100 calls per day for normal operations

### Optimization Features
- React Query caching for reduced API calls
- Efficient database queries with proper indexing
- Background processing for non-blocking operations
- Intelligent retry mechanisms for reliability

---

## 🎯 Production Ready

The HotelReviewHub system is **production-ready** with all core features implemented, tested, and optimized for reliable hotel review management operations.

**Ready to manage your hotel reviews professionally!** 🏨⭐
