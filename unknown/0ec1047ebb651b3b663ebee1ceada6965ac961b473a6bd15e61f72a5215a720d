# Hotel Review Management System

## Overview

This is a full-stack hotel review management system built with Express.js, React, and TypeScript. The application integrates with Google My Business to automatically fetch hotel reviews, provides sentiment analysis, and allows hotel managers to respond to reviews directly from the dashboard.

## System Architecture

### Frontend Architecture
- **Framework**: React 18 with TypeScript
- **Routing**: Wouter for lightweight client-side routing
- **State Management**: TanStack Query (React Query) for server state management
- **UI Framework**: Radix UI components with shadcn/ui styling
- **Styling**: Tailwind CSS with custom hotel-themed design tokens
- **Build Tool**: Vite for fast development and optimized builds

### Backend Architecture
- **Runtime**: Node.js with Express.js framework
- **Language**: TypeScript with ES modules
- **Database**: PostgreSQL with Drizzle ORM for type-safe database operations
- **Authentication**: Google OAuth 2.0 for Google My Business integration
- **Scheduled Tasks**: Node-cron for automated review synchronization

### Development Environment
- **Platform**: Replit with Node.js 20, Web, and PostgreSQL 16 modules
- **Package Manager**: npm with lock file for dependency consistency
- **Development Server**: Concurrent frontend (Vite) and backend (tsx) development

## Key Components

### Database Schema
- **users**: Basic user authentication table
- **business_info**: Google My Business account and location information
- **reviews**: Complete review data including ratings, comments, responses, and sentiment analysis

### Authentication & Authorization
- Google OAuth 2.0 integration for accessing Google My Business API
- Token refresh mechanism for maintaining API access
- Session-based authentication with automatic token renewal

### Review Management
- Automated review fetching every 30 minutes via cron jobs
- Sentiment analysis (positive/negative/neutral) for each review
- Review response functionality directly through the application
- Rating distribution and statistical analysis

### API Structure
- RESTful API endpoints for business info, reviews, and statistics
- Real-time sync capabilities with manual trigger option
- Proper error handling and status reporting

## Data Flow

1. **Initial Setup**: User authenticates with Google OAuth to connect their Google My Business account
2. **Account Discovery**: System fetches available business accounts and locations
3. **Automated Sync**: Cron job runs every 30 minutes to fetch new reviews
4. **Data Processing**: Reviews are processed for sentiment analysis and stored
5. **Dashboard Display**: Real-time statistics and review lists are presented to users
6. **Response Management**: Users can respond to reviews directly through the interface

## External Dependencies

### Core Dependencies
- **@neondatabase/serverless**: PostgreSQL database connectivity
- **drizzle-orm**: Type-safe database operations and migrations
- **googleapis**: Google My Business API integration
- **@tanstack/react-query**: Server state management
- **@radix-ui/***: Comprehensive UI component library
- **wouter**: Lightweight React routing
- **node-cron**: Scheduled task execution

### Development Dependencies
- **vite**: Frontend build tool and development server
- **tsx**: TypeScript execution for Node.js
- **tailwindcss**: Utility-first CSS framework
- **@replit/vite-plugin-***: Replit-specific development tools

## Deployment Strategy

### Production Build
- Frontend: Vite builds static assets to `dist/public`
- Backend: esbuild bundles server code to `dist/index.js`
- Single deployment artifact with both client and server

### Environment Configuration
- Development: Uses in-memory storage fallback when database unavailable
- Production: Requires PostgreSQL database connection
- OAuth: Google Client ID and Secret for API access
- Platform: Automatic Replit domain detection for OAuth callbacks

### Scalability Considerations
- Replit autoscale deployment target for handling traffic variations
- Efficient database queries with proper indexing
- Caching strategy through React Query for reduced API calls

## Changelog

```
Changelog:
- June 26, 2025: Implemented complete live Google Business integration
  - Removed all demo data from system
  - Configured real Google OAuth credentials (************-7c4dg56v2oa9qm36ikndd358h26qv5di.apps.googleusercontent.com)
  - Set up proper redirect URI (https://86528c88-1213-4591-9e3b-c333bd04be8e-00-1jkf1yxc7zzvm.worf.replit.dev/api/auth/callback)
  - Fixed OAuth callback routing to redirect to dashboard after authentication
  - Added Google API quota limit handling to prevent authentication failures
  - Implemented live review fetching from Google My Business API
  - Added real-time review response posting to Google
  - Configured automated review sync every 30 minutes
  - System now ready for production with live Google Business data
- June 25, 2025: Initial setup
```

## User Preferences

```
Preferred communication style: Simple, everyday language.
```