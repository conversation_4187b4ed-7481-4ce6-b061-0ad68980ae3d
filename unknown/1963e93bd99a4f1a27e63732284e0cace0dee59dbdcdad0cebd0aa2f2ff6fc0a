// package.json
{
  "name": "simple-hotel-reviews",
  "version": "1.0.0",
  "description": "Simple automated hotel review fetcher",
  "main": "app.js",
  "scripts": {
    "start": "node app.js",
    "dev": "nodemon app.js"
  },
  "dependencies": {
    "express": "^4.18.2",
    "googleapis": "^128.0.0",
    "dotenv": "^16.3.1",
    "node-cron": "^3.0.3",
    "fs": "^0.0.1-security",
    "axios": "^1.6.2"
  },
  "devDependencies": {
    "nodemon": "^3.0.2"
  }
}

// .env
NODE_ENV=development
PORT=5000

# Google OAuth Configuration
GOOGLE_CLIENT_ID=************-7c4dg56v2oa9qm36ikndd358h26qv5di.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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
GOOGLE_REDIRECT_URI=http://localhost:5000/callback

# Your Business Configuration (Set these after initial setup)
BUSINESS_ACCOUNT_ID=
BUSINESS_LOCATION_ID=
GOOGLE_REFRESH_TOKEN=

// app.js - Simple All-in-One Review System
const express = require('express');
const { google } = require('googleapis');
const cron = require('node-cron');
const fs = require('fs').promises;
const path = require('path');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 5000;

// Simple in-memory storage (replace with database for production)
let reviews = [];
let businessInfo = {};

// Google OAuth setup
const oauth2Client = new google.auth.OAuth2(
  process.env.GOOGLE_CLIENT_ID,
  process.env.GOOGLE_CLIENT_SECRET,
  process.env.GOOGLE_REDIRECT_URI
);

// Middleware
app.use(express.json());
app.use(express.static('public'));

// ============================================
// SIMPLE SETUP - ONE-TIME CONFIGURATION
// ============================================

// Step 1: Get authorization URL
app.get('/setup', (req, res) => {
  const authUrl = oauth2Client.generateAuthUrl({
    access_type: 'offline',
    scope: ['https://www.googleapis.com/auth/business.manage'],
    prompt: 'consent'
  });
  
  res.send(`
    <h1>🏨 Hotel Review System Setup</h1>
    <h2>Step 1: Authorize Google Access</h2>
    <a href="${authUrl}" target="_blank" style="
      background: #4285f4; 
      color: white; 
      padding: 15px 30px; 
      text-decoration: none; 
      border-radius: 5px;
      display: inline-block;
      margin: 20px 0;
    ">🔐 Authorize Google Business Profile</a>
    <p>Click the link above, authorize the app, and you'll be redirected back automatically.</p>
  `);
});

// Step 2: Handle OAuth callback and setup
app.get('/callback', async (req, res) => {
  try {
    const { code } = req.query;
    const { tokens } = await oauth2Client.getToken(code);
    oauth2Client.setCredentials(tokens);
    
    // Get business accounts
    const mybusiness = google.mybusinessbusinessinformation('v1');
    google.options({ auth: oauth2Client });
    
    const accountsResponse = await mybusiness.accounts.list();
    const accounts = accountsResponse.data.accounts || [];
    
    if (accounts.length === 0) {
      return res.send('<h1>❌ No business accounts found</h1>');
    }
    
    // Get locations for first account
    const account = accounts[0];
    const locationsResponse = await mybusiness.accounts.locations.list({
      parent: account.name
    });
    const locations = locationsResponse.data.locations || [];
    
    if (locations.length === 0) {
      return res.send('<h1>❌ No business locations found</h1>');
    }
    
    // Save configuration
    const config = {
      BUSINESS_ACCOUNT_ID: account.name,
      BUSINESS_LOCATION_ID: locations[0].name,
      GOOGLE_REFRESH_TOKEN: tokens.refresh_token
    };
    
    // Update .env file
    await updateEnvFile(config);
    
    // Store business info
    businessInfo = {
      name: locations[0].title,
      address: locations[0].storefrontAddress?.addressLines?.join(', '),
      accountId: account.name,
      locationId: locations[0].name
    };
    
    res.send(`
      <h1>✅ Setup Complete!</h1>
      <h2>Business: ${businessInfo.name}</h2>
      <p>Address: ${businessInfo.address}</p>
      <p><strong>🎉 Your review system is now configured and running!</strong></p>
      <p>Reviews will be automatically fetched every 30 minutes.</p>
      <div style="margin: 20px 0;">
        <a href="/reviews" style="background: #34a853; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;">📊 View Reviews</a>
        <a href="/sync" style="background: #ea4335; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">🔄 Sync Now</a>
      </div>
      <p><em>Server will automatically restart to load new configuration...</em></p>
      <script>setTimeout(() => window.location.href = '/reviews', 3000);</script>
    `);
    
    // Restart server to load new env variables
    setTimeout(() => {
      console.log('🔄 Restarting server to load new configuration...');
      process.exit(0);
    }, 2000);
    
  } catch (error) {
    console.error('Setup error:', error);
    res.send(`<h1>❌ Setup failed: ${error.message}</h1>`);
  }
});

// ============================================
// AUTOMATED REVIEW FETCHING
// ============================================

// Initialize OAuth with stored refresh token
const initializeAuth = () => {
  if (process.env.GOOGLE_REFRESH_TOKEN) {
    oauth2Client.setCredentials({
      refresh_token: process.env.GOOGLE_REFRESH_TOKEN
    });
    console.log('✅ Google OAuth initialized with stored refresh token');
    return true;
  }
  console.log('⚠️ No refresh token found. Please run setup first: http://localhost:5000/setup');
  return false;
};

// Main function to fetch reviews
const fetchReviews = async () => {
  try {
    if (!process.env.BUSINESS_LOCATION_ID) {
      console.log('⚠️ Business not configured. Please run setup first.');
      return;
    }
    
    // Refresh access token
    await oauth2Client.getAccessToken();
    google.options({ auth: oauth2Client });
    
    const mybusinessreviews = google.mybusinessreviews('v1');
    
    console.log('🔄 Fetching reviews...');
    
    const response = await mybusinessreviews.accounts.locations.reviews.list({
      parent: process.env.BUSINESS_LOCATION_ID,
      pageSize: 50
    });
    
    const fetchedReviews = response.data.reviews || [];
    
    // Process and store reviews
    const newReviews = [];
    const currentTime = new Date();
    
    for (const review of fetchedReviews) {
      const reviewData = {
        id: review.name,
        rating: review.starRating,
        comment: review.comment || '',
        reviewerName: review.reviewer?.displayName || 'Anonymous',
        reviewDate: new Date(review.createTime),
        response: review.reviewReply ? {
          comment: review.reviewReply.comment,
          date: new Date(review.reviewReply.updateTime)
        } : null,
        sentiment: review.starRating >= 4 ? 'positive' : review.starRating <= 2 ? 'negative' : 'neutral',
        fetchedAt: currentTime
      };
      
      // Check if review already exists
      const existingIndex = reviews.findIndex(r => r.id === reviewData.id);
      if (existingIndex >= 0) {
        reviews[existingIndex] = reviewData;
      } else {
        reviews.unshift(reviewData);
        newReviews.push(reviewData);
      }
    }
    
    // Save to file for persistence
    await saveReviewsToFile();
    
    console.log(`✅ Fetched ${fetchedReviews.length} reviews (${newReviews.length} new)`);
    
    if (newReviews.length > 0) {
      console.log('🆕 New reviews:');
      newReviews.forEach(review => {
        console.log(`   ⭐ ${review.rating}/5 - ${review.reviewerName}: ${review.comment.substring(0, 100)}...`);
      });
    }
    
    return newReviews;
    
  } catch (error) {
    console.error('❌ Error fetching reviews:', error.message);
    return [];
  }
};

// Save reviews to file for persistence
const saveReviewsToFile = async () => {
  try {
    const dataDir = path.join(__dirname, 'data');
    await fs.mkdir(dataDir, { recursive: true });
    await fs.writeFile(
      path.join(dataDir, 'reviews.json'), 
      JSON.stringify(reviews, null, 2)
    );
  } catch (error) {
    console.error('Error saving reviews:', error);
  }
};

// Load reviews from file on startup
const loadReviewsFromFile = async () => {
  try {
    const filePath = path.join(__dirname, 'data', 'reviews.json');
    const data = await fs.readFile(filePath, 'utf8');
    reviews = JSON.parse(data);
    console.log(`📁 Loaded ${reviews.length} reviews from file`);
  } catch (error) {
    console.log('📁 No existing reviews file found, starting fresh');
    reviews = [];
  }
};

// ============================================
// WEB INTERFACE
// ============================================

// Main reviews dashboard
app.get('/reviews', async (req, res) => {
  const { rating, sentiment, limit = 20 } = req.query;
  
  let filteredReviews = [...reviews];
  
  // Apply filters
  if (rating) {
    filteredReviews = filteredReviews.filter(r => r.rating == rating);
  }
  if (sentiment) {
    filteredReviews = filteredReviews.filter(r => r.sentiment === sentiment);
  }
  
  // Limit results
  filteredReviews = filteredReviews.slice(0, parseInt(limit));
  
  // Calculate stats
  const totalReviews = reviews.length;
  const avgRating = reviews.length > 0 ? 
    (reviews.reduce((sum, r) => sum + r.rating, 0) / reviews.length).toFixed(1) : 0;
  const ratingCounts = [1,2,3,4,5].map(rating => 
    reviews.filter(r => r.rating === rating).length
  );
  const sentimentCounts = {
    positive: reviews.filter(r => r.sentiment === 'positive').length,
    neutral: reviews.filter(r => r.sentiment === 'neutral').length,
    negative: reviews.filter(r => r.sentiment === 'negative').length
  };
  
  res.send(`
    <!DOCTYPE html>
    <html>
    <head>
        <title>🏨 Hotel Reviews Dashboard</title>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <style>
            * { margin: 0; padding: 0; box-sizing: border-box; }
            body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; background: #f5f5f5; }
            .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
            .header { background: white; padding: 30px; border-radius: 10px; margin-bottom: 30px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 20px 0; }
            .stat-card { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 10px; text-align: center; }
            .stat-number { font-size: 2.5em; font-weight: bold; }
            .stat-label { opacity: 0.9; margin-top: 5px; }
            .filters { background: white; padding: 20px; border-radius: 10px; margin-bottom: 20px; }
            .filter-group { display: inline-block; margin-right: 20px; }
            .filter-group select, .filter-group input { padding: 8px 12px; border: 1px solid #ddd; border-radius: 5px; }
            .reviews-grid { display: grid; gap: 20px; }
            .review-card { background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }
            .review-header { display: flex; justify-content: between; align-items: center; margin-bottom: 15px; }
            .reviewer-info { display: flex; align-items: center; }
            .reviewer-name { font-weight: bold; margin-right: 10px; }
            .review-date { color: #666; font-size: 0.9em; }
            .rating { display: flex; align-items: center; }
            .stars { color: #ffc107; margin-right: 10px; }
            .sentiment { padding: 4px 8px; border-radius: 15px; font-size: 0.8em; font-weight: bold; }
            .sentiment.positive { background: #d4edda; color: #155724; }
            .sentiment.negative { background: #f8d7da; color: #721c24; }
            .sentiment.neutral { background: #e2e3e5; color: #383d41; }
            .review-content { line-height: 1.6; margin-bottom: 15px; }
            .review-response { background: #f8f9fa; padding: 15px; border-left: 4px solid #007bff; border-radius: 5px; }
            .response-label { font-weight: bold; color: #007bff; margin-bottom: 5px; }
            .actions { margin-top: 20px; }
            .btn { padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; text-decoration: none; display: inline-block; margin-right: 10px; }
            .btn-primary { background: #007bff; color: white; }
            .btn-success { background: #28a745; color: white; }
            .btn-warning { background: #ffc107; color: #212529; }
            .auto-sync-status { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🏨 Hotel Reviews Dashboard</h1>
                <p><strong>Business:</strong> ${businessInfo.name || 'Not configured'}</p>
                <div class="auto-sync-status">
                    <strong>🔄 Auto-Sync Status:</strong> Active (Every 30 minutes) | 
                    Last update: ${reviews.length > 0 ? new Date(reviews[0].fetchedAt).toLocaleString() : 'Never'}
                </div>
                
                <div class="stats">
                    <div class="stat-card">
                        <div class="stat-number">${totalReviews}</div>
                        <div class="stat-label">Total Reviews</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">${avgRating}</div>
                        <div class="stat-label">Average Rating</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">${sentimentCounts.positive}</div>
                        <div class="stat-label">Positive Reviews</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">${sentimentCounts.negative}</div>
                        <div class="stat-label">Negative Reviews</div>
                    </div>
                </div>
                
                <div class="actions">
                    <a href="/sync" class="btn btn-success">🔄 Sync Now</a>
                    <a href="/reviews" class="btn btn-primary">📊 All Reviews</a>
                    <a href="/export" class="btn btn-warning">📥 Export CSV</a>
                </div>
            </div>
            
            <div class="filters">
                <div class="filter-group">
                    <label>Rating:</label>
                    <select onchange="updateFilter('rating', this.value)">
                        <option value="">All Ratings</option>
                        <option value="5" ${rating === '5' ? 'selected' : ''}>5 Stars (${ratingCounts[4]})</option>
                        <option value="4" ${rating === '4' ? 'selected' : ''}>4 Stars (${ratingCounts[3]})</option>
                        <option value="3" ${rating === '3' ? 'selected' : ''}>3 Stars (${ratingCounts[2]})</option>
                        <option value="2" ${rating === '2' ? 'selected' : ''}>2 Stars (${ratingCounts[1]})</option>
                        <option value="1" ${rating === '1' ? 'selected' : ''}>1 Star (${ratingCounts[0]})</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label>Sentiment:</label>
                    <select onchange="updateFilter('sentiment', this.value)">
                        <option value="">All Sentiments</option>
                        <option value="positive" ${sentiment === 'positive' ? 'selected' : ''}>Positive (${sentimentCounts.positive})</option>
                        <option value="neutral" ${sentiment === 'neutral' ? 'selected' : ''}>Neutral (${sentimentCounts.neutral})</option>
                        <option value="negative" ${sentiment === 'negative' ? 'selected' : ''}>Negative (${sentimentCounts.negative})</option>
                    </select>
                </div>
            </div>
            
            <div class="reviews-grid">
                ${filteredReviews.map(review => `
                    <div class="review-card">
                        <div class="review-header">
                            <div class="reviewer-info">
                                <span class="reviewer-name">${review.reviewerName}</span>
                                <span class="review-date">${review.reviewDate.toLocaleDateString()}</span>
                            </div>
                            <div class="rating">
                                <span class="stars">${'⭐'.repeat(review.rating)}</span>
                                <span class="sentiment ${review.sentiment}">${review.sentiment}</span>
                            </div>
                        </div>
                        <div class="review-content">${review.comment}</div>
                        ${review.response ? `
                            <div class="review-response">
                                <div class="response-label">Business Response:</div>
                                <div>${review.response.comment}</div>
                            </div>
                        ` : ''}
                    </div>
                `).join('')}
            </div>
            
            ${filteredReviews.length === 0 ? '<div style="text-align: center; padding: 50px; color: #666;">No reviews found matching your criteria.</div>' : ''}
        </div>
        
        <script>
            function updateFilter(param, value) {
                const url = new URL(window.location);
                if (value) {
                    url.searchParams.set(param, value);
                } else {
                    url.searchParams.delete(param);
                }
                window.location = url;
            }
            
            // Auto-refresh every 5 minutes
            setTimeout(() => window.location.reload(), 300000);
        </script>
    </body>
    </html>
  `);
});

// Manual sync endpoint
app.get('/sync', async (req, res) => {
  const newReviews = await fetchReviews();
  res.json({ 
    success: true, 
    message: `Synced successfully! Found ${newReviews.length} new reviews.`,
    newReviews: newReviews
  });
});

// Export reviews as CSV
app.get('/export', (req, res) => {
  const csv = [
    'Date,Rating,Reviewer,Comment,Sentiment,Response',
    ...reviews.map(r => `"${r.reviewDate.toISOString()}",${r.rating},"${r.reviewerName}","${r.comment.replace(/"/g, '""')}","${r.sentiment}","${r.response ? r.response.comment.replace(/"/g, '""') : ''}"`)
  ].join('\n');
  
  res.setHeader('Content-Type', 'text/csv');
  res.setHeader('Content-Disposition', 'attachment; filename=reviews.csv');
  res.send(csv);
});

// ============================================
// UTILITIES
// ============================================

// Update .env file with new configuration
const updateEnvFile = async (newConfig) => {
  try {
    const envPath = path.join(__dirname, '.env');
    let envContent = await fs.readFile(envPath, 'utf8');
    
    Object.entries(newConfig).forEach(([key, value]) => {
      const regex = new RegExp(`^${key}=.*$`, 'm');
      if (envContent.match(regex)) {
        envContent = envContent.replace(regex, `${key}=${value}`);
      } else {
        envContent += `\n${key}=${value}`;
      }
    });
    
    await fs.writeFile(envPath, envContent);
    console.log('✅ Environment configuration updated');
  } catch (error) {
    console.error('Error updating .env file:', error);
  }
};

// ============================================
// AUTOMATED SCHEDULING
// ============================================

// Schedule automatic review fetching every 30 minutes
cron.schedule('*/30 * * * *', async () => {
  console.log('🔄 Scheduled review sync starting...');
  await fetchReviews();
});

// Also fetch immediately on startup (after 10 seconds)
setTimeout(async () => {
  if (process.env.BUSINESS_LOCATION_ID) {
    console.log('🚀 Initial review fetch on startup...');
    await fetchReviews();
  }
}, 10000);

// ============================================
// SERVER STARTUP
// ============================================

const startServer = async () => {
  // Load existing reviews
  await loadReviewsFromFile();
  
  // Initialize Google OAuth
  const authReady = initializeAuth();
  
  app.listen(PORT, () => {
    console.log(`🚀 Simple Hotel Review System running on http://localhost:${PORT}`);
    console.log('');
    if (!authReady) {
      console.log('⚠️ SETUP REQUIRED:');
      console.log(`   Visit: http://localhost:${PORT}/setup`);
      console.log('   Complete the one-time Google authorization');
      console.log('');
    } else {
      console.log('✅ System ready! Auto-fetching reviews every 30 minutes');
      console.log(`📊 Dashboard: http://localhost:${PORT}/reviews`);
      console.log('');
    }
  });
};

startServer();

// Graceful shutdown
process.on('SIGINT', async () => {
  console.log('\n🛑 Shutting down gracefully...');
  await saveReviewsToFile();
  process.exit(0);
});