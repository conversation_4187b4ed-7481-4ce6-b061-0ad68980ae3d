import { useEffect, useState } from "react";
import { useLocation } from "wouter";
import { useBusinessInfo } from "@/hooks/use-business-info";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Building2, ExternalLink, CheckCircle, Clock, AlertCircle } from "lucide-react";

export default function Setup() {
  const [, setLocation] = useLocation();
  const { data: businessData, isLoading } = useBusinessInfo();
  const [setupStatus, setSetupStatus] = useState<'pending' | 'connecting' | 'complete' | 'error'>('pending');
  const [errorMessage, setErrorMessage] = useState<string>('');

  useEffect(() => {
    // Check URL params for setup completion or errors
    const urlParams = new URLSearchParams(window.location.search);
    const setupParam = urlParams.get('setup');
    const messageParam = urlParams.get('message');
    
    if (setupParam === 'complete') {
      setSetupStatus('complete');
      setTimeout(() => {
        setLocation('/');
      }, 3000);
    } else if (setupParam === 'error') {
      setSetupStatus('error');
      setErrorMessage(messageParam || 'An unknown error occurred');
    }
  }, [setLocation]);

  useEffect(() => {
    if (!isLoading && businessData?.isSetup) {
      setLocation('/');
    }
  }, [businessData, isLoading, setLocation]);

  const handleAuthorizeGoogle = () => {
    if (businessData?.setupUrl) {
      setSetupStatus('connecting');
      window.location.href = businessData.setupUrl;
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading setup...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 flex items-center justify-center p-4">
      <div className="w-full max-w-2xl">
        <div className="text-center mb-8">
          <div className="w-16 h-16 bg-gradient-primary rounded-full flex items-center justify-center mx-auto mb-4">
            <Building2 className="h-8 w-8 text-white" />
          </div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Hotel Review Management</h1>
          <p className="text-gray-600">Connect your Google Business Profile to start managing reviews</p>
        </div>

        {setupStatus === 'complete' ? (
          <Card>
            <CardContent className="pt-6 text-center">
              <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
              <h2 className="text-xl font-semibold text-gray-900 mb-2">Setup Complete!</h2>
              <p className="text-gray-600 mb-4">
                Your Google Business Profile has been connected successfully. 
                Redirecting to dashboard...
              </p>
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary mx-auto"></div>
            </CardContent>
          </Card>
        ) : setupStatus === 'error' ? (
          <Card>
            <CardContent className="pt-6">
              <div className="text-center mb-6">
                <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
                <h2 className="text-xl font-semibold text-gray-900 mb-2">Setup Failed</h2>
                <p className="text-gray-600 mb-4">{errorMessage}</p>
              </div>
              
              <Alert className="mb-6">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  This usually happens when the authorization code expires. Please try again.
                </AlertDescription>
              </Alert>

              <div className="space-y-4">
                <Button 
                  onClick={() => {
                    setSetupStatus('pending');
                    setErrorMessage('');
                    window.history.replaceState({}, '', window.location.pathname);
                  }}
                  className="w-full bg-hotel-blue hover:bg-hotel-blue/90"
                  size="lg"
                >
                  Try Again
                </Button>
                
                <div className="text-sm text-gray-600">
                  <p className="font-medium mb-2">Common solutions:</p>
                  <ul className="list-disc list-inside space-y-1">
                    <li>Make sure you have a Google Business Profile</li>
                    <li>Complete the authorization quickly after clicking</li>
                    <li>Check that you granted all requested permissions</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        ) : (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Building2 className="h-5 w-5" />
                Connect Your Business
              </CardTitle>
              <CardDescription>
                Authorize access to automatically fetch and manage your Google Business Profile reviews.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {setupStatus === 'connecting' && (
                <Alert>
                  <Clock className="h-4 w-4" />
                  <AlertDescription>
                    Connecting to Google Business Profile... Please complete the authorization in the popup window.
                  </AlertDescription>
                </Alert>
              )}

              <div className="space-y-3">
                <div className="flex items-center gap-3 p-3 bg-green-50 rounded-lg">
                  <CheckCircle className="h-5 w-5 text-green-600" />
                  <span className="text-sm text-gray-700">Automatic review synchronization</span>
                </div>
                <div className="flex items-center gap-3 p-3 bg-green-50 rounded-lg">
                  <CheckCircle className="h-5 w-5 text-green-600" />
                  <span className="text-sm text-gray-700">Real-time review analytics</span>
                </div>
                <div className="flex items-center gap-3 p-3 bg-green-50 rounded-lg">
                  <CheckCircle className="h-5 w-5 text-green-600" />
                  <span className="text-sm text-gray-700">Response management tools</span>
                </div>
                <div className="flex items-center gap-3 p-3 bg-green-50 rounded-lg">
                  <CheckCircle className="h-5 w-5 text-green-600" />
                  <span className="text-sm text-gray-700">Sentiment analysis</span>
                </div>
              </div>

              <div className="border-t pt-6">
                <div className="flex items-center gap-2 mb-4">
                  <h3 className="font-medium text-gray-900">Required Permissions</h3>
                  <Badge variant="secondary">Secure</Badge>
                </div>
                <div className="text-sm text-gray-600 space-y-1">
                  <p>• Read your business profile information</p>
                  <p>• Access customer reviews</p>
                  <p>• Post responses to reviews</p>
                </div>
              </div>

              <Button 
                onClick={handleAuthorizeGoogle}
                disabled={setupStatus === 'connecting' || !businessData?.setupUrl}
                className="w-full bg-hotel-blue hover:bg-hotel-blue/90"
                size="lg"
              >
                <ExternalLink className="h-4 w-4 mr-2" />
                {setupStatus === 'connecting' ? 'Connecting...' : 'Authorize Google Business Profile'}
              </Button>

              <Alert>
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  This app requires access to your Google Business Profile to fetch reviews. 
                  Your data is never shared with third parties.
                </AlertDescription>
              </Alert>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
