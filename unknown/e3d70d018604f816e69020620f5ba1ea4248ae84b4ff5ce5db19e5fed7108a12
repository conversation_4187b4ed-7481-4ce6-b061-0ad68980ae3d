import { Card, CardContent } from "@/components/ui/card";
import { useQuery } from "@tanstack/react-query";
import { Star, TrendingUp, MessageCircle, Calendar } from "lucide-react";

export default function StatisticsCards() {
  const { data: stats, isLoading } = useQuery({
    queryKey: ['/api/reviews/stats'],
  });

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {[...Array(4)].map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardContent className="p-6">
              <div className="h-16 bg-gray-200 rounded"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`h-4 w-4 ${
          i < Math.floor(rating) 
            ? 'text-hotel-yellow fill-current' 
            : i < rating 
            ? 'text-hotel-yellow fill-current opacity-50' 
            : 'text-gray-300'
        }`}
      />
    ));
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <Card className="gradient-primary text-white">
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-white/80 text-sm font-medium">Total Reviews</p>
              <p className="text-3xl font-bold">{stats?.totalReviews || 0}</p>
            </div>
            <div className="bg-white/20 rounded-lg p-3">
              <Star className="h-6 w-6" />
            </div>
          </div>
          <div className="mt-4">
            <span className="text-green-200 text-sm font-medium">
              +{stats?.thisMonth || 0} this month
            </span>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-600 text-sm font-medium">Average Rating</p>
              <p className="text-3xl font-bold text-gray-900">
                {stats?.averageRating || 0}
              </p>
            </div>
            <div className="bg-yellow-50 rounded-lg p-3">
              <TrendingUp className="h-6 w-6 text-hotel-yellow" />
            </div>
          </div>
          <div className="mt-4 flex items-center">
            <div className="flex">
              {renderStars(stats?.averageRating || 0)}
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-600 text-sm font-medium">Response Rate</p>
              <p className="text-3xl font-bold text-gray-900">
                {stats?.responseRate || 0}%
              </p>
            </div>
            <div className="bg-green-50 rounded-lg p-3">
              <MessageCircle className="h-6 w-6 text-hotel-green" />
            </div>
          </div>
          <div className="mt-4">
            <span className="text-hotel-green text-sm font-medium">
              Great response rate!
            </span>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-600 text-sm font-medium">This Month</p>
              <p className="text-3xl font-bold text-gray-900">
                {stats?.thisMonth || 0}
              </p>
            </div>
            <div className="bg-blue-50 rounded-lg p-3">
              <Calendar className="h-6 w-6 text-hotel-blue" />
            </div>
          </div>
          <div className="mt-4">
            <span className="text-hotel-blue text-sm font-medium">
              New reviews received
            </span>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
