{"version": "6", "dialect": "sqlite", "id": "87dd0a0e-c102-47cf-950e-2564445a19d4", "prevId": "********-0000-0000-0000-************", "tables": {"business_info": {"name": "business_info", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "address": {"name": "address", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "account_id": {"name": "account_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "location_id": {"name": "location_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "refresh_token": {"name": "refresh_token", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "last_sync": {"name": "last_sync", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "reviews": {"name": "reviews", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "google_id": {"name": "google_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "rating": {"name": "rating", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "comment": {"name": "comment", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "''"}, "reviewer_name": {"name": "reviewer_name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "review_date": {"name": "review_date", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "response_comment": {"name": "response_comment", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "response_date": {"name": "response_date", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "sentiment": {"name": "sentiment", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "fetched_at": {"name": "fetched_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {"reviews_google_id_unique": {"name": "reviews_google_id_unique", "columns": ["google_id"], "isUnique": true}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "users": {"name": "users", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "username": {"name": "username", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {"users_username_unique": {"name": "users_username_unique", "columns": ["username"], "isUnique": true}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}}, "views": {}, "enums": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "internal": {"indexes": {}}}