CREATE TABLE `business_info` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`name` text NOT NULL,
	`address` text,
	`account_id` text NOT NULL,
	`location_id` text NOT NULL,
	`refresh_token` text,
	`last_sync` text
);
--> statement-breakpoint
CREATE TABLE `reviews` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`google_id` text NOT NULL,
	`rating` integer NOT NULL,
	`comment` text DEFAULT '',
	`reviewer_name` text NOT NULL,
	`review_date` text NOT NULL,
	`response_comment` text,
	`response_date` text,
	`sentiment` text NOT NULL,
	`fetched_at` text NOT NULL
);
--> statement-breakpoint
CREATE UNIQUE INDEX `reviews_google_id_unique` ON `reviews` (`google_id`);--> statement-breakpoint
CREATE TABLE `users` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`username` text NOT NULL,
	`password` text NOT NULL
);
--> statement-breakpoint
CREATE UNIQUE INDEX `users_username_unique` ON `users` (`username`);