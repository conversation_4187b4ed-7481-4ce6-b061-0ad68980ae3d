import { useState } from "react";
import { useLocation } from "wouter";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useBusinessInfo } from "@/hooks/use-business-info";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import { Building2, RefreshCw, Clock, LogOut } from "lucide-react";
import { formatDistanceToNow } from "date-fns";

export default function NavigationHeader() {
  const { data: businessData } = useBusinessInfo();
  const { toast } = useToast();
  const [syncing, setSyncing] = useState(false);
  const [loggingOut, setLoggingOut] = useState(false);
  const [, setLocation] = useLocation();

  const handleSync = async () => {
    setSyncing(true);
    try {
      const response = await apiRequest('POST', '/api/sync');
      const result = await response.json();

      if (result.success) {
        toast({
          title: "Sync Completed",
          description: `Found ${result.newReviews} new reviews`,
        });
      } else if (result.quotaExceeded) {
        toast({
          title: "Google API Quota Exceeded",
          description: result.message || "API quota limits reached. Your 163 reviews will be fetched automatically when quota resets.",
          variant: "default",
        });
      } else {
        toast({
          title: "Sync Failed",
          description: result.message || result.error || "Failed to sync reviews",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Sync Error",
        description: "Failed to sync reviews. Please try again later.",
        variant: "destructive",
      });
    } finally {
      setSyncing(false);
    }
  };

  const handleLogout = async () => {
    setLoggingOut(true);
    try {
      const response = await apiRequest('/api/logout', {
        method: 'POST'
      });

      if (response.success) {
        toast({
          title: "Logged out successfully",
          description: "You have been disconnected from your Google account.",
        });
        // Redirect to setup page after logout
        setTimeout(() => {
          setLocation("/setup");
        }, 1000);
      } else {
        toast({
          title: "Logout failed",
          description: response.error || "Unable to logout. Please try again.",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Logout error",
        description: "Failed to logout. Please try again later.",
        variant: "destructive",
      });
    } finally {
      setLoggingOut(false);
    }
  };

  const lastSyncText = businessData?.businessInfo?.lastSync 
    ? `${formatDistanceToNow(new Date(businessData.businessInfo.lastSync))} ago`
    : 'Never';

  return (
    <nav className="bg-white border-b border-slate-200 sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-3">
              <Building2 className="text-hotel-blue text-2xl" />
              <h1 className="text-xl font-semibold text-slate-900">
                {businessData?.businessInfo?.name || 'Hotel Reviews'}
              </h1>
            </div>
            <Badge variant="secondary">Review Management</Badge>
          </div>
          
          <div className="flex items-center space-x-4">
            <Button
              onClick={handleSync}
              disabled={syncing}
              className="bg-hotel-blue hover:bg-hotel-blue/90"
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${syncing ? 'animate-spin' : ''}`} />
              {syncing ? 'Syncing...' : 'Sync Reviews'}
            </Button>

            <Button
              onClick={handleLogout}
              disabled={loggingOut}
              variant="outline"
              className="text-red-600 border-red-200 hover:bg-red-50 hover:border-red-300"
            >
              <LogOut className={`h-4 w-4 mr-2 ${loggingOut ? 'animate-pulse' : ''}`} />
              {loggingOut ? 'Logging out...' : 'Logout'}
            </Button>

            <div className="flex items-center space-x-2 text-sm text-slate-600">
              <Clock className="h-4 w-4" />
              <span>Last sync: {lastSyncText}</span>
            </div>
          </div>
        </div>
      </div>
    </nav>
  );
}
