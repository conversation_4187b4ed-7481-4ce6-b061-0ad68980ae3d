import { <PERSON>, CardContent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { useQuery } from "@tanstack/react-query";
import { Star } from "lucide-react";

export default function RatingDistribution() {
  const { data: stats, isLoading } = useQuery({
    queryKey: ['/api/reviews/stats'],
  });

  if (isLoading) {
    return (
      <Card className="mb-8">
        <CardHeader>
          <CardTitle>Rating Distribution</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="flex items-center animate-pulse">
                <div className="w-8 h-4 bg-gray-200 rounded mr-4"></div>
                <div className="flex-1 h-2 bg-gray-200 rounded mr-4"></div>
                <div className="w-12 h-4 bg-gray-200 rounded"></div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  const ratingCounts = stats?.ratingCounts || [0, 0, 0, 0, 0];
  const total = ratingCounts.reduce((sum, count) => sum + count, 0);

  const getBarColor = (rating: number) => {
    if (rating >= 4) return 'bg-hotel-green';
    if (rating === 3) return 'bg-hotel-yellow';
    return 'bg-hotel-red';
  };

  return (
    <Card className="mb-8">
      <CardHeader>
        <CardTitle className="text-lg font-semibold text-slate-900">
          Rating Distribution
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {[5, 4, 3, 2, 1].map((rating, index) => {
            const count = ratingCounts[rating - 1];
            const percentage = total > 0 ? (count / total) * 100 : 0;
            
            return (
              <div key={rating} className="flex items-center">
                <div className="flex items-center space-x-2 w-20">
                  <span className="text-sm font-medium text-slate-700">
                    {rating}
                  </span>
                  <Star className="h-4 w-4 text-hotel-yellow fill-current" />
                </div>
                <div className="flex-1 mx-4">
                  <div className="bg-slate-200 rounded-full h-2">
                    <div
                      className={`h-2 rounded-full ${getBarColor(rating)}`}
                      style={{ width: `${percentage}%` }}
                    />
                  </div>
                </div>
                <span className="text-sm text-slate-600 w-12 text-right">
                  {count}
                </span>
              </div>
            );
          })}
        </div>
      </CardContent>
    </Card>
  );
}
